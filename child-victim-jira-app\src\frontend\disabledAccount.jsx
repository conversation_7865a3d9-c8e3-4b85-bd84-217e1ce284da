import React, { useState, useEffect } from 'react';
import { Label, Textfield, Checkbox, ErrorMessage } from '@forge/react';
import { invoke } from '@forge/bridge';

export const AccountDisabledFields = ({ context }) => {
  const [isTemporarilyDisabled, setIsTemporarilyDisabled] = useState(false);
  const [disabledDateTemporarily, setDisabledDateTemporarily] = useState('');
  const [userNotifiedTemporarily, setUserNotifiedTemporarily] = useState('');
  const [userNotifiedDateTemporarily, setUserNotifiedDateTemporarily] = useState('');
  const [reenabledDateTemporarily, setReenabledDateTemporarily] = useState('');

  const [disabledDateErrorTemporarily, setDisabledDateErrorTemporarily] = useState(undefined);
  const [notifiedDateErrorTemporarily, setNotifiedDateErrorTemporarily] = useState(undefined);
  const [reenabledDateErrorTemporarily, setReenabledDateErrorTemporarily] = useState(undefined);

  const [isPermanentlyDisabled, setIsPermanentlyDisabled] = useState(false);
  const [disabledDatePermanently, setDisabledDatePermanently] = useState('');
  const [userNotifiedPermanently, setUserNotifiedPermanently] = useState('');
  const [userNotifiedDatePermanently, setUserNotifiedDatePermanently] = useState('');

  const [disabledDateErrorPermanently, setDisabledDateErrorPermanently] = useState(undefined);
  const [notifiedDateErrorPermanently, setNotifiedDateErrorPermanently] = useState(undefined);

  const onChangeIsDisabled = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    const value = e.target.checked;
    setIsTemporarilyDisabled(value);
    invoke('setIsDisabledTemporarily', { cloudId: context.cloudId, issueId: key, isDisabled: value });
  };

  const onChangeDisabledDate = (e) => {
    const now = new Date();
    if (new Date(e.target.value) > now) {
      setDisabledDateErrorTemporarily("The date must be in past");
    } else {
      setDisabledDateTemporarily(e.target.value);
      setDisabledDateErrorTemporarily(undefined);
    }
  };

  const onBlurDisabledDate = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setDisabledDateTemporarily', { cloudId: context.cloudId, issueId: key, disabledDate: e.target.value });
  };

  const onChangeUserNotified = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    const value = e.target.checked;
    setUserNotifiedTemporarily(value);
    invoke('setUserNotifiedTemporarily', { cloudId: context.cloudId, issueId: key, userNotified: value });
  };

  const onChangeUserNotifiedDate = (e) => {
    const now = new Date();
    const newDate = new Date(e.target.value);
    if (newDate > now) {
      setNotifiedDateErrorTemporarily("The date must be in past");
    } else if (newDate < new Date(disabledDateTemporarily)) {
      setNotifiedDateErrorTemporarily("The date must be on or after the disabled date");
    } else {
      setUserNotifiedDateTemporarily(e.target.value);
      setNotifiedDateErrorTemporarily(undefined);
    }
  };

  const onBlurUserNotifiedDate = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setUserNotifiedDateTemporarily', { cloudId: context.cloudId, issueId: key, userNotifiedDate: e.target.value });
  };

  const onChangeReenabledDate = (e) => {
    const now = new Date();
    const newDate = new Date(e.target.value);
    if (newDate > now) {
      setReenabledDateErrorTemporarily("The date must be in past");
    } else if (newDate < new Date(disabledDateTemporarily)) {
      setReenabledDateErrorTemporarily("The date must be on or after the disabled date");
    } else {
      setReenabledDateTemporarily(e.target.value);
      setReenabledDateErrorTemporarily(undefined);
    }
  };

  const onBlurReenabledDate = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setReenabledDateTemporarily', { cloudId: context.cloudId, issueId: key, reenabledDate: e.target.value });
  };

  const onChangeIsDisabledPermanently = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    const value = e.target.checked;
    setIsPermanentlyDisabled(value);
    invoke('setIsDisabledPermanently', { cloudId: context.cloudId, issueId: key, isDisabled: value });
  };

  const onChangeDisabledDatePermanently = (e) => {
    const now = new Date();
    if (new Date(e.target.value) > now) {
      setDisabledDateErrorPermanently("The date must be in past");
    } else {
      setDisabledDatePermanently(e.target.value);
      setDisabledDateErrorPermanently(undefined);
    }
  };

  const onBlurDisabledDatePermanently = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setDisabledDatePermanently', { cloudId: context.cloudId, issueId: key, disabledDate: e.target.value });
  };

  const onChangeUserNotifiedPermanently = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    const value = e.target.checked;
    setUserNotifiedPermanently(value);
    invoke('setUserNotifiedPermanently', { cloudId: context.cloudId, issueId: key, userNotified: value });
  };

  const onChangeUserNotifiedDatePermanently = (e) => {
    const now = new Date();
    const newDate = new Date(e.target.value);
    if (newDate > now) {
      setNotifiedDateErrorPermanently("The date must be in past");
    } else if (newDate < new Date(disabledDatePermanently)) {
      setNotifiedDateErrorPermanently("The date must be on or after the disabled date");
    } else {
      setUserNotifiedDatePermanently(e.target.value);
      setNotifiedDateErrorPermanently(undefined);
    }
  };

  const onBlurUserNotifiedDatePermanently = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setUserNotifiedDatePermanently', { cloudId: context.cloudId, issueId: key, userNotifiedDate: e.target.value });
  };

  useEffect(() => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;

    const { key } = context.extension.issue;

    const fetchAccountStatusData = async () => {
      try {
        const [
          isDisabledTemporarily,
          disabledDateTemporarily,
          userNotifiedTemporarily,
          userNotifiedDateTemporarily,
          reenabledDateTemporarily,
          isDisabledPermanently,
          disabledDatePermanently,
          userNotifiedPermanently,
          userNotifiedDatePermanently
        ] = await Promise.all([
          invoke('getIsDisabledTemporarily', { issueId: key }),
          invoke('getDisabledDateTemporarily', { issueId: key }),
          invoke('getUserNotifiedTemporarily', { issueId: key }),
          invoke('getUserNotifiedDateTemporarily', { issueId: key }),
          invoke('getReenabledDateTemporarily', { issueId: key }),
          invoke('getIsDisabledPermanently', { issueId: key }),
          invoke('getDisabledDatePermanently', { issueId: key }),
          invoke('getUserNotifiedPermanently', { issueId: key }),
          invoke('getUserNotifiedDatePermanently', { issueId: key }),
        ]);

        setIsTemporarilyDisabled(isDisabledTemporarily);
        setDisabledDateTemporarily(disabledDateTemporarily);
        setUserNotifiedTemporarily(userNotifiedTemporarily);
        setUserNotifiedDateTemporarily(userNotifiedDateTemporarily);
        setReenabledDateTemporarily(reenabledDateTemporarily);
        setIsPermanentlyDisabled(isDisabledPermanently);
        setDisabledDatePermanently(disabledDatePermanently);
        setUserNotifiedPermanently(userNotifiedPermanently);
        setUserNotifiedDatePermanently(userNotifiedDatePermanently);
      } catch (error) {
        console.error('Failed to fetch account status data:', error);
      }
    };

    fetchAccountStatusData();
  }, [context]);

  return (
    <>
      <Label labelFor="isDisabled">Account Temporarily Disabled?</Label>
      <Checkbox id="isDisabled" isChecked={isTemporarilyDisabled} onChange={onChangeIsDisabled} />
      <Label labelFor="disabledDate">Disabled Date</Label>
      <Textfield type='date' id="disabledDate" value={disabledDateTemporarily} onChange={onChangeDisabledDate} onBlur={onBlurDisabledDate} isDisabled={!isTemporarilyDisabled} />
      {disabledDateErrorTemporarily && <ErrorMessage>{disabledDateErrorTemporarily}</ErrorMessage>}
      <Label labelFor="userNotified">User Notified?</Label>
      <Checkbox id="userNotified" isChecked={userNotifiedTemporarily} onChange={onChangeUserNotified} isDisabled={!isTemporarilyDisabled} />
      <Label labelFor="userNotifiedDate">User Notified Date</Label>
      <Textfield type='date' id="userNotifiedDate" value={userNotifiedDateTemporarily} onChange={onChangeUserNotifiedDate} onBlur={onBlurUserNotifiedDate} isDisabled={!isTemporarilyDisabled || !userNotifiedTemporarily} />
      {notifiedDateErrorTemporarily && <ErrorMessage>{notifiedDateErrorTemporarily}</ErrorMessage>}
      <Label labelFor="reenabledDate">Re-enabled Date</Label>
      <Textfield id="reenabledDate" type='date' value={reenabledDateTemporarily} onChange={onChangeReenabledDate} onBlur={onBlurReenabledDate} isDisabled={!isTemporarilyDisabled} />
      {reenabledDateErrorTemporarily && <ErrorMessage>{reenabledDateErrorTemporarily}</ErrorMessage>}

      <Label labelFor="isDisabled">Account Permanently Disabled?</Label>
      <Checkbox id="isDisabled" isChecked={isPermanentlyDisabled} onChange={onChangeIsDisabledPermanently} />
      <Label labelFor="disabledDate">Disabled Date</Label>
      <Textfield type='date' id="disabledDate" value={disabledDatePermanently} onChange={onChangeDisabledDatePermanently} onBlur={onBlurDisabledDatePermanently} isDisabled={!isPermanentlyDisabled} />
      {disabledDateErrorPermanently && <ErrorMessage>{disabledDateErrorPermanently}</ErrorMessage>}
      <Label labelFor="userNotified">User Notified?</Label>
      <Checkbox id="userNotified" isChecked={userNotifiedPermanently} onChange={onChangeUserNotifiedPermanently} isDisabled={!isPermanentlyDisabled} />
      <Label labelFor="userNotifiedDate">User Notified Date</Label>
      <Textfield type='date' id="userNotifiedDate" value={userNotifiedDatePermanently} onChange={onChangeUserNotifiedDatePermanently} onBlur={onBlurUserNotifiedDatePermanently} isDisabled={!isPermanentlyDisabled || !userNotifiedPermanently} />
      {notifiedDateErrorPermanently && <ErrorMessage>{notifiedDateErrorPermanently}</ErrorMessage>}
    </>
  );
};
