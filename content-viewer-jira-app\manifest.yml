modules:
  jira:issuePanel:
    - key: t2gp-social-report-child-abuse-jira-app-hello-world-issue-panel
      resource: main
      resolver:
        function: resolver
      render: native
      title: t2gp-social-report-child-abuse-jira-app
      icon: https://developer.atlassian.com/platform/forge/images/icons/issue-panel-icon.svg
  function:
    - key: resolver
      handler: index.handler
resources:
  - key: main
    path: src/frontend/index.jsx
app:
  runtime:
    name: nodejs22.x
  id: ari:cloud:ecosystem::app/b1f1d033-747c-48bc-9705-89f639f53083
permissions:
  external:
    fetch:
      backend:
        - 'https://webhook.site/62a20797-b80b-4196-bd2d-22699a7eed75/*'