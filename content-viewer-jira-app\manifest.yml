modules:
  jira:issuePanel:
    - key: t2gp-social-report-child-abuse-jira-app-hello-world-issue-panel
      resource: main
      resolver:
        function: resolver
      render: native
      title: T2GP Legal Escalation Evidence Viewer
      icon: https://developer.atlassian.com/platform/forge/images/icons/issue-panel-icon.svg
  function:
    - key: resolver
      handler: index.handler
resources:
  - key: main
    path: src/frontend/index.jsx
app:
  runtime:
    name: nodejs22.x
  id: ari:cloud:ecosystem::app/b1f1d033-747c-48bc-9705-89f639f53083
permissions:
  scopes:
    - read:jira-user
  external:
    fetch:
      backend:
        - 'https://t2gp-social-legal-api.dev.d2dragon.net/*'