import React from "react";
import { AuditLog } from "./dataModel";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";

interface AuditLogTableProps {
  issueId: string | undefined;
  auditLogList: AuditLog[];
  isLoading: boolean;
}

const AuditLogTable = (props: AuditLogTableProps) => {
  return (
    <DataTable
      header="Audit Logs"
      style={{
        minWidth: "1200px",
        justifyContent: "center",
      }}
      value={props.auditLogList}
      paginator
      rows={10}
      rowsPerPageOptions={[10, 25]}
      loading={props.isLoading}
      loadingIcon="pi pi-spinner-dotted"
    > 
        <Column header="Event Time" field="eventTime" sortable />
        <Column header="User" field="userId" />
        <Column header="Remote IP Address" field="remoteIpAddress" />
        <Column header="Event Type" field="eventType" />
        <Column header="Event Message" field="eventMessage" />
    </DataTable>
  );
};

export default AuditLogTable;
