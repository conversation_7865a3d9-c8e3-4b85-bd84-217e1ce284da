import React from "react";
import { AuditLog } from "./dataModel";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { DataTablePtOptions, HeaderTitleStyle } from "./style";

interface AuditLogTableProps {
  issueId: string | undefined;
  auditLogList: AuditLog[];
  isLoading: boolean;
}

const AuditLogTable = (props: AuditLogTableProps) => {
  const header = () => (
    <span style={HeaderTitleStyle}>Audit Logs</span>
  );

  return (
    <DataTable
      header={header()}
      value={props.auditLogList}
      paginator
      rows={10}
      rowsPerPageOptions={[10, 25]}
      loading={props.isLoading}
      loadingIcon="pi pi-spinner-dotted"
      pt={DataTablePtOptions}
    >
      <Column header="Event Time" field="eventTime" sortable />
      <Column header="User" field="userId" />
      <Column header="Remote IP Address" field="remoteIpAddress" />
      <Column header="Event Type" field="eventType" />
      <Column header="Event Message" field="eventMessage" />
    </DataTable>
  );
};

export default AuditLogTable;
