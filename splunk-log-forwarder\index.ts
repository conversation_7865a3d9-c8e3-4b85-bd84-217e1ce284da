import { DynamoD<PERSON><PERSON>am<PERSON><PERSON><PERSON>, DynamoDBR<PERSON>ord } from 'aws-lambda';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';
import { unmarshall } from '@aws-sdk/util-dynamodb';
import axios from 'axios';
import { AttributeValue } from '@aws-sdk/client-dynamodb';
// comments
const CRIBL_HTTP_ENDPOINT = process.env.CRIBL_HTTP_ENDPOINT!;
const CRIBL_SECRET_NAME = process.env.CRIBL_SECRET_NAME;

const secretsClient = new SecretsManagerClient({region: process.env.AWS_REGION ?? "us-east-1"});

const getCriblAuthToken = async (): Promise<string> => {
  const command = new GetSecretValueCommand({ SecretId: CRIBL_SECRET_NAME });
  const response = await secretsClient.send(command);

  if (!response.SecretString) throw new Error('SecretString is empty');
  return response.SecretString;
};

const extractNewImage = (record: DynamoDBRecord): Record<string, any> | null => {
  const newImage = record.dynamodb?.NewImage;
  if (!newImage) return null;

  try {
    return unmarshall(newImage as Record<string, AttributeValue>);
  } catch (error) {
    console.error('Failed to unmarshall NewImage:', error);
    return null;
  }
};

export const handler: DynamoDBStreamHandler = async (event) => {
  if (event.Records === undefined || event.Records.length === 0) {
    console.log('No records to process.');
    return;
  }

  let payloads = event.Records
    .map(extractNewImage)
    .filter((p): p is Record<string, any> => p !== null);

  if (payloads.length === 0) {
    console.log('No new images to forward.');
    return;
  }

  const events = payloads.map(e => JSON.stringify({ event: e })).join('\n');

  try {
    const token = await getCriblAuthToken();

    const response = await axios.post(CRIBL_HTTP_ENDPOINT, events, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Splunk ${token}`,
      },
      transformRequest: [(data) => data],
      timeout: 5000,
    });

    console.log(`Forwarded ${payloads.length} new images to Cribl. Status: ${response.status}`);
  } catch (err) {
    console.error('Failed to send data to Cribl:', err);
    throw err;
  }
};
