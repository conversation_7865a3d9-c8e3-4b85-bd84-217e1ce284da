import Resolver from '@forge/resolver';
import { kvs } from '@forge/kvs';
import {
  reporter<PERSON><PERSON>,
  reporterMappingConverter,
  reportedPersonKV,
  reportedPersonMappingConverter,
  lawEnforcementKV,
  lawEnforcementMappingConverter,
  internetDetailsKV,
  internetDetailsMappingConverter,
  intendedRecipientKV,
  intendedRecipientMappingConverter,
  incidentInformationKV,
  incidentInformationMappingConverter,
  childVictimKV,
  childVictimMappingConverter,
} from './mapping';

const resolver = new Resolver();

async function getHelper(key) {
  const storedValue = await kvs.get(key);
  console.log('Key: ', key, 'Value: ', storedValue);
  return storedValue || '';
}

async function getPrefixedKVValues(issueId, object) {
  const keys = Object.keys(object);
  const fullKeys = keys.map(key => `${issueId}-${key}`);

  const values = await Promise.all(fullKeys.map(getHelper));
  const result = {...object};
  keys.forEach((key, i) => {
    result[key] = values[i];
  });

  return result;
}

async function fetchValues(issueId) {
  const [
    reporter,
    // reportedPerson,
    // lawEnforcement,
    // internetDetails,
    // intendedRecipient,
    // incidentInformation,
    // childVictim
  ] = await Promise.all([
    getPrefixedKVValues(issueId, reporterKV()),
    // getPrefixedKVValues(issueId, reportedPersonKV()),
    // getPrefixedKVValues(issueId, lawEnforcementKV()),
    // getPrefixedKVValues(issueId, internetDetailsKV()),
    // getPrefixedKVValues(issueId, intendedRecipientKV()),
    // getPrefixedKVValues(issueId, incidentInformationKV()),
    // getPrefixedKVValues(issueId, childVictimKV()),
  ]);

  return {
    'reporter': reporterMappingConverter(reporter),
    // 'reportedPerson': reportedPersonMappingConverter(reportedPerson),
    // 'lawEnforcement': lawEnforcementMappingConverter(lawEnforcement),
    // 'internetDetails': internetDetailsMappingConverter(internetDetails),
    // 'intendedRecipient': intendedRecipientMappingConverter(intendedRecipient),
    // 'incidentInformation': incidentInformationMappingConverter(incidentInformation),
    // 'childVictim': childVictimMappingConverter(childVictim),
    // 'issueId': issueId,
  };
}

resolver.define('getValues', ({ payload }) => {
  return fetchValues(payload.issueId);
});

export const handler = resolver.getDefinitions();
