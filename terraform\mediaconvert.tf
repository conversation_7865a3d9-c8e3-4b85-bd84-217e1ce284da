resource "aws_s3_bucket" "mediaconvert_bucket" {
  bucket = "${var.prefix}-report-evidence"
  tags   = local.provider_default_tags
}

resource "aws_iam_role" "mediaconvert_role" {
  name = "${var.prefix}-MediaConvertServiceRole"
  tags = local.provider_default_tags
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow",
      Principal = {
        Service = "mediaconvert.amazonaws.com"
      },
      Action = "sts:AssumeRole"
    }]
  })
}

resource "aws_iam_role_policy" "mediaconvert_s3_policy" {
  name = "${var.prefix}-MediaConvertBucketAccess"
  role = aws_iam_role.mediaconvert_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid      = "ReadInputFiles",
        Effect   = "Allow",
        Action   = ["s3:GetObject"],
        Resource = "${aws_s3_bucket.mediaconvert_bucket.arn}/input/*"
      },
      {
        Sid      = "WriteOutputFiles",
        Effect   = "Allow",
        Action   = ["s3:PutObject"],
        Resource = "${aws_s3_bucket.mediaconvert_bucket.arn}/output/*"
      }
    ]
  })
}


resource "aws_iam_role" "lambda_role" {
  name = "${var.prefix}-LambdaMediaConvertSubmitRole"
  tags = local.provider_default_tags
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Effect = "Allow",
      Principal = {
        Service = "lambda.amazonaws.com"
      },
      Action = "sts:AssumeRole"
    }]
  })
}

resource "aws_iam_policy" "lambda_policy" {
  name = "${var.prefix}-LambdaSubmitMediaConvertJobPolicy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid    = "SubmitMediaConvertJobs",
        Effect = "Allow",
        Action = [
          "mediaconvert:CreateJob"
        ],
        Resource = "*"
      },
      {
        Sid      = "PassMediaConvertRole",
        Effect   = "Allow",
        Action   = "iam:PassRole",
        Resource = aws_iam_role.mediaconvert_role.arn
      },
      {
        Sid    = "AccessS3Bucket",
        Effect = "Allow",
        Action = [
          "s3:GetObject",
          "s3:PutObject"
        ],
        Resource = [
          "${aws_s3_bucket.mediaconvert_bucket.arn}/input/*",
          "${aws_s3_bucket.mediaconvert_bucket.arn}/output/*"
        ]
      },
      {
        Sid    = "LogPermissions",
        Effect = "Allow",
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_policy_attachment" "attach_lambda_policy" {
  name       = "${var.prefix}-lambda-mediaconvert-policy"
  policy_arn = aws_iam_policy.lambda_policy.arn
  roles      = [aws_iam_role.lambda_role.name]
}


data "archive_file" "lambda_mediaconvert" {
  type        = "zip"
  source_dir  = "../mediaconvert"
  output_path = "package/lambda_function_mediaconvert.zip"
}

resource "aws_lambda_function" "mediaconvert_submit" {
  function_name    = "${var.prefix}-SubmitMediaConvertJob"
  description      = var.description
  runtime          = "python3.11"
  handler          = "lambda_function.lambda_handler"
  role             = aws_iam_role.lambda_role.arn
  timeout          = 30
  tags             = local.provider_default_tags
  filename         = "package/lambda_function_mediaconvert.zip"
  source_code_hash = data.archive_file.lambda_mediaconvert.output_base64sha256
  environment {
    variables = {
      BUCKET_NAME        = aws_s3_bucket.mediaconvert_bucket.bucket
      MEDIACONVERT_ROLE  = aws_iam_role.mediaconvert_role.arn
      MEDIACONVERT_QUEUE = aws_media_convert_queue.custom_queue.arn
    }
  }
}

resource "aws_media_convert_queue" "custom_queue" {
  name         = "${var.prefix}-custom-mediaconvert-queue"
  description  = "Queue for Lambda-submitted jobs"
  pricing_plan = "ON_DEMAND" # or "RESERVED"

  tags = local.provider_default_tags
}

resource "aws_lambda_permission" "allow_s3_invoke" {
  statement_id  = "AllowExecutionFromS3"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.mediaconvert_submit.function_name
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.mediaconvert_bucket.arn
}

resource "aws_s3_bucket_notification" "lambda_trigger" {
  bucket = aws_s3_bucket.mediaconvert_bucket.id

  lambda_function {
    lambda_function_arn = aws_lambda_function.mediaconvert_submit.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "input/"
    filter_suffix       = ".mp4"
  }

  depends_on = [aws_lambda_permission.allow_s3_invoke]
}
