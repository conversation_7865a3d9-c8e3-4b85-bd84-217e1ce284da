import React, { useState, useEffect } from 'react';
import { Textfield, Inline, Icon, Tooltip, Label, Select, Checkbox, ErrorMessage } from '@forge/react';
import { invoke } from '@forge/bridge';
import { COUNTRY_CODES } from './address'

export const LocationInfo = ({ context }) => {
  const [city, setCity] = useState('');
  const [region, setRegion] = useState('');
  const [countryCode, setCountryCode] = useState('');
  const [verified, setVerified] = useState(false);
  const [dateTime, setDateTime] = useState('');
  const [error, setError] = useState(undefined);

  const onChangeCity = (e) => {
    setCity(e.target.value);
  };

  const onBlurCity = (e) => {
    invoke('setEstimatedLocationCity', { context, city: e.target.value });
  };

  const onChangeRegion = (e) => {
    setRegion(e.target.value);
  };

  const onBlurRegion = (e) => {
    invoke('setEstimatedLocationRegion', { context, region: e.target.value });
  };

  const onChangeCountryCode = (value) => {
    setCountryCode(value);
    invoke('setEstimatedLocationCountryCode', { context, countryCode: value });
  };

  const onChangeVerified = (e) => {
    setVerified(e.target.checked);
    invoke('setEstimatedLocationVerified', { context, verified: e.target.checked });
  };

  const onChangeDate = (e) => {
    const now = new Date();
    if (new Date(e.target.value) > now) {
      setError("The date must be in past");
    } else {
      setDateTime(e.target.value);
      invoke('setEstimatedLocationTimestamp', { context, timestamp: e.target.value });
      setError(undefined);
    }
  };

  useEffect(() => {
    // Data will be populated via populateFieldsFromDDB function from parent component
    // Individual getter functions have been removed in favor of a single getReport function
  }, [context]);

  return (
    <>
      <Label labelFor="city">Estimated Location City</Label>
      <Textfield id="city" value={city} onChange={onChangeCity} onBlur={onBlurCity} maxLength={255} />

      <Inline alignInline="start" space='space.050'>
        <Label labelFor="region">Reporter's Estimated Region</Label>
        <Tooltip content="The region of the estimated location.\n It must be a valid US state or territory postal abbreviation if supplied and <countryCode> is 'US'. \n must be not be blank if <city> is supplied and <countryCode> is 'US'">
          <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
        </Tooltip>
      </Inline>
      <Textfield id="region" value={region} onBlur={onBlurRegion} onChange={onChangeRegion} />

      <Label labelFor="countryCode">Reporter's Estimated Country</Label>
      <Select id="countryCode" options={COUNTRY_CODES} value={countryCode} onChange={onChangeCountryCode} />

      <Inline alignInline="start" space='space.050'>
        <Label labelFor="verified">Reporter's Location Verified</Label>
        <Tooltip content="Whether the reporter has verified the estimated location.">
          <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
        </Tooltip>
      </Inline>
      <Checkbox value="checkbox" isChecked={verified} onChange={onChangeVerified} />

      <Inline alignInline="start" space='space.050'>
        <Label labelFor="date">Reporter's Estimated Location Time</Label>
        <Tooltip content="The date the reporter made the location estimation.">
          <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
        </Tooltip>
      </Inline>
      <Textfield type='datetime-local' id="date" value={dateTime} onChange={onChangeDate} />
      {error && <ErrorMessage>{error}</ErrorMessage>}
    </>
  );
};
