import React, { useEffect, useState } from 'react';
import ForgeRecon<PERSON><PERSON>, { Icon, Inline, Stack, Textfield, Label, Tabs, TabList, Tooltip, Tab, TabPanel, Checkbox } from '@forge/react';
import { invoke, requestJira, view } from '@forge/bridge';
import { LocationInfo } from './estimatedLocation'
import { DeviceEventFields } from './device';
import { PersonFields } from './person';
import { IpCaptureEventFields } from './ipCaptureEvent';
import { AccountDisabledFields } from './disabledAccount';

const App = () => {
  const [vehicleDescription, setVehicleDescription] = useState('')
  const [espService, setEspService] = useState('')
  const [screenName, setScreenName] = useState('')
  const [displayName, setDisplayName] = useState('')
  const [profileUrl, setProfileUrl] = useState('')
  const [profileBio, setProfileBio] = useState('')
  const [espIdentifer, setEspIdentifer] = useState('')
  const [compromisedAccount, setCompromisedAccount] = useState(false);
  const [groupIdentifier, setGroupIdentifier] = useState('');
  const [thirdPartyUserReported, setThirdPartyUserReported] = useState(false);
  const [priorCTReports, setPriorCTReports] = useState('');
  const [additionalInfo, setAdditionalInfo] = useState('');

  const productContextRef = React.useRef(null);

  const onChangeVehicleDescription = (e) => {
    setVehicleDescription(e.target.value);
  };

  const onBlurVehicleDescription = (e) => {
    invoke('setVehicleDescription', { context: productContextRef.current, vehicleDescription: e.target.value });
  };

  const onChangeEspService = (e) => {
    setEspService(e.target.value);
  };

  const onBlurEspService = (e) => {
    invoke('setEspService', { context: productContextRef.current, espService: e.target.value });
  };

  const onChangeScreenName = (e) => {
    setScreenName(e.target.value);
  };

  const onBlurScreenName = (e) => {
    invoke('setScreenName', { context: productContextRef.current, screenName: e.target.value });
  };

  const onChangeDisplayName = (e) => {
    setDisplayName(e.target.value);
  };

  const onBlurDisplayName = (e) => {
    invoke('setDisplayName', { context: productContextRef.current, displayName: e.target.value });
  };

  const onChangeProfileUrl = (e) => {
    setProfileUrl(e.target.value);
  };

  const onBlurProfileUrl = (e) => {
    invoke('setProfileUrl', { context: productContextRef.current, profileUrl: e.target.value });
  };
  const onChangeProfileBio = (e) => {
    setProfileBio(e.target.value);
  };

  const onBlurProfileBio = (e) => {
    invoke('setProfileBio', { context: productContextRef.current, profileBio: e.target.value });
  };

  const onChangeEspIdentifer = (e) => {
    setEspIdentifer(e.target.value);
  };

  const onBlurEspIdentifer = (e) => {
    invoke('setEspIdentifer', { context: productContextRef.current, espIdentifer: e.target.value });
  };

  const onChangeCompromisedAccount = (e) => {
    const value = e.target.checked;
    setCompromisedAccount(value);
    invoke('setCompromisedAccount', { context: productContextRef.current, compromisedAccount: value });
  };

  const onChangeGroupIdentifier = (e) => {
    setGroupIdentifier(e.target.value);
  };

  const onBlurGroupIdentifier = (e) => {
    invoke('setGroupIdentifier', { context: productContextRef.current, groupIdentifier: e.target.value });
  };

  const onChangeThirdPartyUserReported = (e) => {
    const value = e.target.checked;
    setThirdPartyUserReported(value);
    invoke('setThirdPartyUserReported', { context: productContextRef.current, thirdPartyUserReported: value });
  };

  const onChangePriorCTReports = (e) => {
    setPriorCTReports(e.target.value);
  };

  const onBlurPriorCTReports = (e) => {
    invoke('setPriorCTReports', { context: productContextRef.current, priorCTReports: e.target.value });
  };

  const onChangeAdditionalInfo = (e) => {
    setAdditionalInfo(e.target.value);
  };

  const onBlurAdditionalInfo = (e) => {
    invoke('setAdditionalInfo', { context: productContextRef.current, additionalInfo: e.target.value });
  };

  useEffect(() => {
    const fetchPersonData = async () => {
      try {
        const context = await view.getContext();
        if (context.accountId) {
          const res = await requestJira(`/rest/api/3/user?accountId=${context.accountId}`);
          const userData = await res.json();
          productContextRef.current = { user: userData.displayName };
        }

        productContextRef.current = {
          ...productContextRef.current,
          cloudId: context.cloudId,
          issueId: context.extension.issue.key
        };

        const [
          vehicleDescription,
          espService,
          screenName,
          displayName,
          profileUrl,
          profileBio,
          espIdentifer,
          compromisedAccount,
          groupIdentifier,
          thirdPartyUserReported,
          priorCTReports,
          additionalInfo
        ] = await Promise.all([
          invoke('getVehicleDescription', { issueId: productContextRef.current.issueId }),
          invoke('getEspService', { issueId: productContextRef.current.issueId }),
          invoke('getScreenName', { issueId: productContextRef.current.issueId }),
          invoke('getDisplayName', { issueId: productContextRef.current.issueId }),
          invoke('getProfileUrl', { issueId: productContextRef.current.issueId }),
          invoke('getProfileBio', { issueId: productContextRef.current.issueId }),
          invoke('getEspIdentifer', { issueId: productContextRef.current.issueId }),
          invoke('getCompromisedAccount', { issueId: productContextRef.current.issueId }),
          invoke('getGroupIdentifier', { issueId: productContextRef.current.issueId }),
          invoke('getThirdPartyUserReported', { issueId: productContextRef.current.issueId }),
          invoke('getPriorCTReports', { issueId: productContextRef.current.issueId }),
          invoke('getAdditionalInfo', { issueId: productContextRef.current.issueId }),
        ]);

        setVehicleDescription(vehicleDescription);
        setEspService(espService);
        setScreenName(screenName);
        setDisplayName(displayName);
        setProfileUrl(profileUrl);
        setProfileBio(profileBio);
        setEspIdentifer(espIdentifer);
        setCompromisedAccount(compromisedAccount);
        setGroupIdentifier(groupIdentifier);
        setThirdPartyUserReported(thirdPartyUserReported);
        setPriorCTReports(priorCTReports);
        setAdditionalInfo(additionalInfo);

      } catch (error) {
        console.error('Failed to fetch view context:', error);
      }
    };

    fetchPersonData();
  }, []);

  const reportedPersonTab = () => {
    return (
      <Stack alignInline='stretch' grow='fill'>
        <PersonFields context={productContextRef.current} />

        <Label labelFor="vehicle">Vehicle Description</Label>
        <Textfield id="vehicle" value={vehicleDescription} onChange={onChangeVehicleDescription} onBlur={onBlurVehicleDescription} maxLength={300} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="espService">ESP Service</Label>
          <Tooltip content="The name of the reporter’s product or service that was used by the reported person or user during the incident.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="espService" value={espService} onChange={onChangeEspService} onBlur={onBlurEspService} maxLength={100} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="screenName">Screen Name</Label>
          <Tooltip content="The screen name of the reported person or user.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="screenName" value={screenName} onChange={onChangeScreenName} onBlur={onBlurScreenName} maxLength={256} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="displayName">Display Name</Label>
          <Tooltip content="A display name, other than a screen name or a username, for the reported person or user.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="displayName" value={displayName} onChange={onChangeDisplayName} onBlur={onBlurDisplayName} maxLength={256} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="profileUrl">Profile URL</Label>
          <Tooltip content="An identifying URL for the reported person or user.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="profileUrl" value={profileUrl} onChange={onChangeProfileUrl} onBlur={onBlurProfileUrl} maxLength={2083} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="profileBio">Profile Bio</Label>
          <Tooltip content="A copy of the user’s bio as per their account according to the reporting company’s platform.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="profileBio" value={profileBio} onChange={onChangeProfileBio} onBlur={onBlurProfileBio} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="espIdentifier">ESP Identifier</Label>
          <Tooltip content="The unique ID of the reported person or user in the reporter’s system.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="espIdentifier" value={espIdentifer} onChange={onChangeEspIdentifer} onBlur={onBlurEspIdentifer} maxLength={256} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="compromisedAccount">Compromised Account</Label>
          <Tooltip content="This account has been compromised, hacked, or hijacked.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Checkbox id="compromisedAccount" isChecked={compromisedAccount} onChange={onChangeCompromisedAccount} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="groupIdentifier">Group Identifier</Label>
          <Tooltip content="Unique group identifiers (e.g., group name, group ID) if the reporter believes the reported person or user is engaged in an online group related to child sexual exploitation.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="groupIdentifier" value={groupIdentifier} onChange={onChangeGroupIdentifier} onBlur={onBlurGroupIdentifier} maxLength={256} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="thirdPartyUserReported">Third Party User Reported</Label>
          <Tooltip content="Whether the reported person or user is using another company’s service and the reporting company has no further information about this person or user.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Checkbox id="thirdPartyUserReported" isChecked={thirdPartyUserReported} onChange={onChangeThirdPartyUserReported} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="priorCTReports">Prior CT Reports</Label>
          <Tooltip content="A report ID for a prior CyberTipline report on this reported person or user.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield type='number' id="priorCTReports" value={priorCTReports} onChange={onChangePriorCTReports} onBlur={onBlurPriorCTReports} />

        <Label labelFor="additionalInfo">Additional Info</Label>
        <Textfield id="additionalInfo" value={additionalInfo} onChange={onChangeAdditionalInfo} onBlur={onBlurAdditionalInfo} />
      </Stack>
    )
  }

  const reportedPersonContactTab = () => {
    return (
      <Stack alignInline='stretch' grow='fill'>
        <LocationInfo context={productContextRef.current} />
        <DeviceEventFields context={productContextRef.current} />
      </Stack>
    )
  }

  return (
    <Tabs id="reportedPerson-tabs">
      <TabList>
        <Tab>Reported Person</Tab>
        <Tab>Additional Information</Tab>
        <Tab>IP Event</Tab>
        <Tab>User Account</Tab>
      </TabList>
      <TabPanel>{reportedPersonTab()}</TabPanel>
      <TabPanel>{reportedPersonContactTab()}</TabPanel>
      <TabPanel>
        <Stack alignInline='stretch' grow='fill'>
          <IpCaptureEventFields context={productContextRef.current} />
        </Stack>
      </TabPanel>
      <TabPanel>
        <Stack alignInline='stretch' grow='fill'>
          <AccountDisabledFields context={productContextRef.current} />
        </Stack>
      </TabPanel>
    </Tabs>
  );
};

ForgeReconciler.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
