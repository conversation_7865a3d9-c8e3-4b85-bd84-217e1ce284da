export const reporterKV = () => ({
  'reporter-first-name': '',
  'reporter-last-name': '',
  'reporter-phone': '',
  'reporter-email': '',
  'reporter-age': '',
  'reporter-date-of-birth': '',
  'reporter-company-template': '',
  'reporter-term-of-service': '',
  'reporter-legal-url': '',
});

export const reporterMappingConverter = (reporter) => ({
  'reporter': {
    'reportingPerson': {
      'firstName': reporter['reporter-first-name'],
      'lastName': reporter['reporter-last-name'],
      'phone': reporter['reporter-phone'],
      'email': reporter['reporter-email'],
      'age': reporter['reporter-age'],
      'dateOfBirth': reporter['reporter-date-of-birth'],
    },
    'companyTemplate': reporter['reporter-company-template'],
    'termOfService': reporter['reporter-term-of-service'],
    'legalUrl': reporter['reporter-legal-url'],
  }
});

export const reportedPersonKV = () => ({
  'reported-person-first-name': '',
  'reported-person-last-name': '',
  'reported-person-phone': '',
  'reported-person-email': '',
  'reported-person-age': '',
  'reported-person-dob': '',
  'reported-person-street': '',
  'reported-person-city': '',
  'reported-person-zip-code': '',
  'reported-person-state': '',
  'reported-person-non-usa-state': '',
  'reported-person-country': '',
  'reported-person-address-type': '',
  'reported-person-vehicle-description': '',
  'reported-person-esp-service': '',
  'reported-person-screen-name': '',
  'reported-person-display-name': '',
  'reported-person-profile-url': '',
  'reported-person-profile-bio': '',
  'reported-person-compromised-account': '',
  'reported-person-group-identifier': '',
  'reported-person-third-party-user-reported': '',
  'reported-person-prior-ct-reports': '',
  'reported-person-additional-info': '',
  'reported-person-esp-identifier': '',
  'reported-person-device-id-type': '',
  'reported-person-device-id-value': '',
  'reported-person-device-event-name': '',
  'reported-person-device-date-time': '',
  'reported-person-estimated-location-city': '',
  'reported-person-estimated-location-region': '',
  'reported-person-estimated-location-country-code': '',
  'reported-person-estimated-location-verified': '',
  'reported-person-estimated-location-timestamp': '',
  'reported-person-ip-capture-event-ip-address': '',
  'reported-person-ip-capture-event-event-name': '',
  'reported-person-ip-capture-event-date-time': '',
  'reported-person-ip-capture-event-possible-proxy': '',
  'reported-person-ip-capture-event-port': '',
  'reported-person-account-is-disabled-temporarily': '',
  'reported-person-account-disabled-date-temporarily': '',
  'reported-person-account-user-notified-temporarily': '',
  'reported-person-account-user-notified-date-temporarily': '',
  'reported-person-account-reenabled-date-temporarily': '',
  'reported-person-account-is-disabled-Permanently': '',
  'reported-person-account-disabled-date-Permanently': '',
  'reported-person-account-user-notified-Permanently': '',
  'reported-person-account-user-notified-date-Permanently': '',
});

export const reportedPersonMappingConverter = (reportedPerson) => ({
  'personOrUserReported': {
    'personOrUserReportedPerson': {
      'firstName': reportedPerson['reported-person-first-name'],
      'lastName': reportedPerson['reported-person-last-name'],
      'phone': reportedPerson['reported-person-phone'],
      'email': reportedPerson['reported-person-email'],
      'address': {
        'address': reportedPerson['reported-person-street'],
        'city': reportedPerson['reported-person-city'],
        'zipCode': reportedPerson['reported-person-zip-code'],
        'state': reportedPerson['reported-person-state'],
        'nonUsaState': reportedPerson['reported-person-non-usa-state'],
        'country': reportedPerson['reported-person-country'],
        'type': reportedPerson['reported-person-address type'],
      },
      'age': reportedPerson['reported-person-age'],
      'dateOfBirth': reportedPerson['reported-person-dob'],
    },
    'vehicleDescription': reportedPerson['reported-person-vehicle-description'],
    'espService': reportedPerson['reported-person-esp-service'],
    'screenName': reportedPerson['reported-person-screen-name'],
    'displayName': reportedPerson['reported-person-display-name'],
    'profileUrl': reportedPerson['reported-person-profile-url'],
    'profileBio': reportedPerson['reported-person-profile-bio'],
    'compromisedAccount': reportedPerson['reported-person-compromised-account'] === 'true',
    'ipCaptureEvent': {
      'ipAddress': reportedPerson['reported-person-ip-capture-event-ip-address'],
      'eventName': reportedPerson['reported-person-ip-capture-event-event-name'],
      'dateTime': reportedPerson['reported-person-ip-capture-event-date-time'],
      'possibleProxy': reportedPerson['reported-person-ip-capture-event-possible-proxy'] === 'true',
      'port': reportedPerson['reported-person-ip-capture-event-port'],
    },
    'deviceId': {
      'idType': reportedPerson['reported-person-device-id-type'],
      'idValue': reportedPerson['reported-person-device-id-value'],
      'eventName': reportedPerson['reported-person-device-event-name'],
      'dateTime': reportedPerson['reported-person-device-date-time'],
    },
    'thirdPartyUserReported': reportedPerson['reported-person-third-party-user-reported'] === 'true',
    'priorCTReports': reportedPerson['reported-person-prior-ct-reports'],
    'groupIdentifier': reportedPerson['reported-person-group-identifier'],
    'accountTemporarilyDisabled': {
      'isDisabled': reportedPerson['reported-person-account-is-disabled-temporarily'] === 'true',
      'disabledDate': reportedPerson['reported-person-account-disabled-date-temporarily'],
      'userNotified': reportedPerson['reported-person-account-user-notified-temporarily'] === 'true',
      'userNotifiedDate': reportedPerson['reported-person-account-user-notified-date-temporarily'],
      'reenabledDate': reportedPerson['reported-person-account-reenabled-date-temporarily'],
    },
    'accountPermanentlyDisabled': {
      'isDisabled': reportedPerson['reported-person-account-is-disabled-Permanently'] === 'true',
      'disabledDate': reportedPerson['reported-person-account-disabled-date-Permanently'],
      'userNotified': reportedPerson['reported-person-account-user-notified-Permanently'] === 'true',
      'userNotifiedDate': reportedPerson['reported-person-account-user-notified-date-Permanently'],
    },
    'estimatedLocation': {
      'city': reportedPerson['reported-person-estimated-location-city'],
      'region': reportedPerson['reported-person-estimated-location-region'],
      'countryCode': reportedPerson['reported-person-estimated-location-country-code'],
      'verified': reportedPerson['reported-person-estimated-location-verified'] === 'true',
      'timestamp': reportedPerson['reported-person-estimated-location-timestamp'],
    },
    'additionalInfo': reportedPerson['reported-person-additional-info'],
  }
});

export const lawEnforcementKV = () => ({
  'law-enforcement-agency-name': '',
  'law-enforcement-case-number': '',
  'law-enforcement-reported-to-le': '',
  'law-enforcement-served-legal-domestic': '',
  'law-enforcement-served-legal-intl': '',
  'law-enforcement-flea-country': '',
  'law-enforcement-first-name': '',
  'law-enforcement-last-name': '',
  'law-enforcement-phone': '',
  'law-enforcement-email': '',
  'law-enforcement-street': '',
  'law-enforcement-city': '',
  'law-enforcement-zip-code': '',
  'law-enforcement-state': '',
  'law-enforcement-non-usa-state': '',
  'law-enforcement-country': '',
  'law-enforcement-address-type': '',
});

export const lawEnforcementMappingConverter = (lawEnforcement) => ({
  'lawEnforcement': {
    'agencyName': lawEnforcement['law-enforcement-agency-name'],
    'caseNumber': lawEnforcement['law-enforcement-case-number'],
    'reportedToLe': lawEnforcement['law-enforcement-reported-to-le'] === 'true',
    'servedLegalProcessDomestic': lawEnforcement['law-enforcement-served-legal-domestic'] === 'true',
    'servedLegalProcessInternational': lawEnforcement['law-enforcement-served-legal-intl'] === 'true',
    'fleaCountry': lawEnforcement['law-enforcement-flea-country'],
    'officerContact': {
      'firstName': lawEnforcement['law-enforcement-first-name'],
      'lastName': lawEnforcement['law-enforcement-last-name'],
      'phone': lawEnforcement['law-enforcement-phone'],
      'email': lawEnforcement['law-enforcement-email'],
      'address': {
        'address': lawEnforcement['law-enforcement-street'],
        'city': lawEnforcement['law-enforcement-city'],
        'zipCode': lawEnforcement['law-enforcement-zip-code'],
        'state': lawEnforcement['law-enforcement-state'],
        'nonUsaState': lawEnforcement['law-enforcement-non-usa-state'],
        'country': lawEnforcement['law-enforcement-country'],
        'type': lawEnforcement['law-enforcement-address-type'],
      },
    }
  },
});

export const internetDetailsKV = () => ({
  'online-game-console': '',
  'online-game-game-name': '',
  'online-game-content': '',
  'online-game-additional-info': '',
  'internet-details-url': '',
  'internet-details-third-party': '',
  'internet-details-chat-client': '',
  'internet-details-chat-room-name': '',
  'internet-details-content': '',
  'internet-details-page-additional-info': '',
  'internet-details-chat-additional-info': '',
});

export const internetDetailsMappingConverter = (internetDetails) => ({
  'internetDetails': {
    'onlineGamingIncident': {
      'console': internetDetails['online-game-console'],
      'gameName': internetDetails['online-game-game-name'],
      'content': internetDetails['online-game-content'],
      'additionalInfo': internetDetails['online-game-additional-info'],
    },
    'webPageIncident': {
      'url': internetDetails['internet-details-url'],
      'thirdPartyHostedContent': internetDetails['internet-details-third-party'] === 'true',
      'additionalInfo': internetDetails['internet-details-page-additional-info'],
    },
    'chatImIncident': {
      'chatClient': internetDetails['internet-details-chat-client'],
      'chatRoomName': internetDetails['internet-details-chat-room-name'],
      'content': internetDetails['internet-details-content'],
      'additionalInfo': internetDetails['internet-details-chat-additional-info'],
    },
  },
});

export const intendedRecipientKV = () => ({
  'intended-recipient-first-name': '',
  'intended-recipient-last-name': '',
  'intended-recipient-phone': '',
  'intended-recipient-email': '',
  'intended-recipient-age': '',
  'intended-recipient-dob': '',
  'intended-recipient-street': '',
  'intended-recipient-city': '',
  'intended-recipient-zip-code': '',
  'intended-recipient-state': '',
  'intended-recipient-non-usa-state': '',
  'intended-recipient-country': '',
  'intended-recipient-address-type': '',
  'intended-recipient-esp-service': '',
  'intended-recipient-screen-name': '',
  'intended-recipient-display-name': '',
  'intended-recipient-profile-url': '',
  'intended-recipient-profile-bio': '',
  'intended-recipient-compromised-account': '',
  'intended-recipient-group-identifier': '',
  'intended-recipient-third-party-user-reported': '',
  'intended-recipient-prior-ct-reports': '',
  'intended-recipient-additional-info': '',
  'intended-recipient-esp-identifier': '',
  'intended-recipient-device-id-type': '',
  'intended-recipient-device-id-value': '',
  'intended-recipient-device-event-name': '',
  'intended-recipient-device-date-time': '',
  'intended-recipient-estimated-location-city': '',
  'intended-recipient-estimated-location-region': '',
  'intended-recipient-estimated-location-country-code': '',
  'intended-recipient-estimated-location-verified': '',
  'intended-recipient-estimated-location-timestamp': '',
  'intended-recipient-ip-capture-event-ip-address': '',
  'intended-recipient-ip-capture-event-event-name': '',
  'intended-recipient-ip-capture-event-date-time': '',
  'intended-recipient-ip-capture-event-possible-proxy': '',
  'intended-recipient-ip-capture-event-port': '',
  'intended-recipient-account-is-disabled-temporarily': '',
  'intended-recipient-account-disabled-date-temporarily': '',
  'intended-recipient-account-user-notified-temporarily': '',
  'intended-recipient-account-user-notified-date-temporarily': '',
  'intended-recipient-account-reenabled-date-temporarily': '',
  'intended-recipient-account-is-disabled-Permanently': '',
  'intended-recipient-account-disabled-date-Permanently': '',
  'intended-recipient-account-user-notified-Permanently': '',
  'intended-recipient-account-user-notified-date-Permanently': '',
});

export const intendedRecipientMappingConverter = (intendedRecipient) => ({
  'intendedRecipient': {
    'intendedRecipientPerson': {
      'firstName': intendedRecipient['intended-recipient-first-name'],
      'lastName': intendedRecipient['intended-recipient-last-name'],
      'phone': intendedRecipient['intended-recipient-phone'],
      'email': intendedRecipient['intended-recipient-email'],
      'address': {
        'address': intendedRecipient['intended-recipient-street'],
        'city': intendedRecipient['intended-recipient-city'],
        'zipCode': intendedRecipient['intended-recipient-zip-code'],
        'state': intendedRecipient['intended-recipient-state'],
        'nonUsaState': intendedRecipient['intended-recipient-non-usa-state'],
        'country': intendedRecipient['intended-recipient-country'],
        'type': intendedRecipient['intended-recipient-address-type'],
      },
      'age': intendedRecipient['intended-recipient-age'],
      'dateOfBirth': intendedRecipient['intended-recipient-dob'],
    },
    'espIdentifier': intendedRecipient['intended-recipient-esp-identifier'],
    'espService': intendedRecipient['intended-recipient-esp-service'],
    'screenName': intendedRecipient['intended-recipient-screen-name'],
    'displayName': intendedRecipient['intended-recipient-display-name'],
    'profileUrl': intendedRecipient['intended-recipient-profile-url'],
    'profileBio': intendedRecipient['intended-recipient-profile-bio'],
    'compromisedAccount': intendedRecipient['intended-recipient-compromised-account'] === 'true',
    'groupIdentifier': intendedRecipient['intended-recipient-group-identifier'],
    'deviceId': {
      'idType': intendedRecipient['intended-recipient-device-id-type'],
      'idValue': intendedRecipient['intended-recipient-device-id-value'],
      'eventName': intendedRecipient['intended-recipient-device-event-name'],
      'dateTime': intendedRecipient['intended-recipient-device-date-time'],
    },
    'ipCaptureEvent': {
      'ipAddress': intendedRecipient['intended-recipient-ip-capture-event-ip-address'],
      'eventName': intendedRecipient['intended-recipient-ip-capture-event-event-name'],
      'dateTime': intendedRecipient['intended-recipient-ip-capture-event-date-time'],
      'possibleProxy': intendedRecipient['intended-recipient-ip-capture-event-possible-proxy'] === 'true',
      'port': intendedRecipient['intended-recipient-ip-capture-event-port'],
    },
    'accountTemporarilyDisabled': {
      'isDisabled': intendedRecipient['intended-recipient-account-is-disabled-temporarily'] === 'true',
      'disabledDate': intendedRecipient['intended-recipient-account-disabled-date-temporarily'],
      'userNotified': intendedRecipient['intended-recipient-account-user-notified-temporarily'] === 'true',
      'userNotifiedDate': intendedRecipient['intended-recipient-account-user-notified-date-temporarily'],
      'reenabledDate': intendedRecipient['intended-recipient-account-reenabled-date-temporarily'],
    },
    'accountPermanentlyDisabled': {
      'isDisabled': intendedRecipient['intended-recipient-account-is-disabled-Permanently'] === 'true',
      'disabledDate': intendedRecipient['intended-recipient-account-disabled-date-Permanently'],
      'userNotified': intendedRecipient['intended-recipient-account-user-notified-Permanently'] === 'true',
      'userNotifiedDate': intendedRecipient['intended-recipient-account-user-notified-date-Permanently'],
    },
    'additionalInfo': intendedRecipient['intended-recipient-additional-info'],
  }
});

export const incidentInformationKV = () => ({
  'incident-information-platform': '',
  'incident-information-incident-type': '',
  'incident-information-escalate-to-high-priority': '',
  'incident-information-incident-date': '',
  'incident-information-incident-date-description': '',
  'incident-information-report-annotation': '',
});

export const incidentInformationMappingConverter = (incidentInformation) => ({
  'incidentSummary': {
    'platform': incidentInformation['incident-information-platform'],
    'incidentType': incidentInformation['incident-information-incident-type'],
    'escalateToHighPriority': incidentInformation['incident-information-escalate-to-high-priority'] === 'true',
    'incidentDateTime': incidentInformation['incident-information-incident-date'],
    'incidentDateTimeDescription': incidentInformation['incident-information-incident-date-description'],
    'reportAnnotations': incidentInformation['incident-information-report-annotation'],
  }
});

export const childVictimKV = () => ({
  'child-victim-first-name': '',
  'child-victim-last-name': '',
  'child-victim-phone': '',
  'child-victim-email': '',
  'child-victim-age': '',
  'child-victim-dob': '',
  'child-victim-street': '',
  'child-victim-city': '',
  'child-victim-zip-code': '',
  'child-victim-state': '',
  'child-victim-non-usa-state': '',
  'child-victim-country': '',
  'child-victim-address-type': '',
  'child-victim-vehicle-description': '',
  'child-victim-esp-service': '',
  'child-victim-screen-name': '',
  'child-victim-account-disabled-date-Permanently': '',
  'child-victim-account-disabled-date-temporarily': '',
  'child-victim-account-is-disabled-Permanently': '',
  'child-victim-account-is-disabled-temporarily': '',
  'child-victim-account-reenabled-date-temporarily': '',
  'child-victim-account-user-notified-Permanently': '',
  'child-victim-account-user-notified-date-Permanently': '',
  'child-victim-account-user-notified-date-temporarily': '',
  'child-victim-account-user-notified-temporarily': '',
  'child-victim-additional-info': '',
  'child-victim-compromised-account': '',
  'child-victim-device-date-time': '',
  'child-victim-device-event-name': '',
  'child-victim-device-id-type': '',
  'child-victim-device-id-value': '',
  'child-victim-display-name': '',
  'child-victim-esp-identifier': '',
  'child-victim-estimated-location-city': '',
  'child-victim-estimated-location-country-code': '',
  'child-victim-estimated-location-region': '',
  'child-victim-estimated-location-timestamp': '',
  'child-victim-estimated-location-verified': '',
  'child-victim-ip-capture-event-date-time': '',
  'child-victim-ip-capture-event-event-name': '',
  'child-victim-ip-capture-event-ip-address': '',
  'child-victim-ip-capture-event-port': '',
  'child-victim-ip-capture-event-possible-proxy': '',
  'child-victim-prior-ct-reports': '',
  'child-victim-profile-bio': '',
  'child-victim-profile-url': '',
  'child-victim-school-name': '',
  'child-victim-third-party-user-reported': '',
});

export const childVictimMappingConverter = (childVictim) => ({
  'victim': {
    'victimPerson': {
      'firstName': childVictim['child-victim-first-name'],
      'lastName': childVictim['child-victim-last-name'],
      'phone': childVictim['child-victim-phone'],
      'email': childVictim['child-victim-email'],
      'address': {
        'address': childVictim['child-victim-street'],
        'city': childVictim['child-victim-city'],
        'zipCode': childVictim['child-victim-zip-code'],
        'state': childVictim['child-victim-state'],
        'nonUsaState': childVictim['child-victim-non-usa-state'],
        'country': childVictim['child-victim-country'],
        'type': childVictim['child-victim-address-type'],
      },
      'age': childVictim['child-victim-age'],
      'dateOfBirth': childVictim['child-victim-dob'],
    },
    'vehicleDescription': childVictim['child-victim-vehicle-description'],
    'espService': childVictim['child-victim-esp-service'],
    'screenName': childVictim['child-victim-screen-name'],
    'displayName': childVictim['child-victim-display-name'],
    'profileUrl': childVictim['child-victim-profile-url'],
    'profileBio': childVictim['child-victim-profile-bio'],
    'compromisedAccount': childVictim['child-victim-compromised-account'] === 'true',
    'ipCaptureEvent': {
      'ipAddress': childVictim['child-victim-ip-capture-event-ip-address'],
      'eventName': childVictim['child-victim-ip-capture-event-event-name'],
      'dateTime': childVictim['child-victim-ip-capture-event-date-time'],
      'possibleProxy': childVictim['child-victim-ip-capture-event-possible-proxy'] === 'true',
      'port': childVictim['child-victim-ip-capture-event-port'],
    },
    'deviceId': {
      'idType': childVictim['child-victim-device-id-type'],
      'idValue': childVictim['child-victim-device-id-value'],
      'eventName': childVictim['child-victim-device-event-name'],
      'dateTime': childVictim['child-victim-device-date-time'],
    },
    'estimatedLocation': {
      'city': childVictim['child-victim-estimated-location-city'],
      'region': childVictim['child-victim-estimated-location-region'],
      'countryCode': childVictim['child-victim-estimated-location-country-code'],
      'verified': childVictim['child-victim-estimated-location-verified'] === 'true',
      'timestamp': childVictim['child-victim-estimated-location-timestamp'],
    },
    'schoolName': childVictim['child-victim-school-name'],
    'thirdPartyUserReported': childVictim['child-victim-third-party-user-reported'] === 'true',
    'priorCTReports': childVictim['child-victim-prior-ct-reports'],
    'additionalInfo': childVictim['child-victim-additional-info'],
    'accountTemporarilyDisabled': {
      'isDisabled': childVictim['child-victim-account-is-disabled-temporarily'] === 'true',
      'disabledDate': childVictim['child-victim-account-disabled-date-temporarily'],
      'userNotified': childVictim['child-victim-account-user-notified-temporarily'] === 'true',
      'userNotifiedDate': childVictim['child-victim-account-user-notified-date-temporarily'],
      'reenabledDate': childVictim['child-victim-account-reenabled-date-temporarily'],
    },
    'accountPermanentlyDisabled': {
      'isDisabled': childVictim['child-victim-account-is-disabled-Permanently'] === 'true',
      'disabledDate': childVictim['child-victim-account-disabled-date-Permanently'],
      'userNotified': childVictim['child-victim-account-user-notified-Permanently'] === 'true',
      'userNotifiedDate': childVictim['child-victim-account-user-notified-date-Permanently'],
    },
  }
});
