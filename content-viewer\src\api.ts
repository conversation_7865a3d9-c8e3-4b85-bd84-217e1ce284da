import { FileUploadFile } from "primereact/fileupload";
import { FileUploadDetails } from "./dataModel";
import FormData from 'form-data';
import { getApiBaseUrl } from "./utility";

function getDefaultHeader(method: string): RequestInit {
  return {
    method: method,
    redirect: 'manual',
    credentials: 'include'
  }
}

const excludeFileExtensions = "excludeFileExtensions=.mp4,.wmv,.mov,.mkv,.avi,.json";

export function login(issueId: string) {
  window.location.href = `${getApiBaseUrl()}/login?originUrl=${window.location.origin}/evidenceList/${issueId}?${excludeFileExtensions}`;
}

export async function getEvidenceUrls(issueId: string) {
  try {
    const response = await fetch(
      `${getApiBaseUrl()}/evidenceList/${issueId}?${excludeFileExtensions}`,
      getDefaultHeader("GET")
    );

    if (response.status === 200) {
      return response.json();
    } else if (response.status === 401) {
      throw new Error("Unauthorized");
    } else {
      const errorText = await response.text();
      throw new Error(`Unexpected error (${response.status}): ${errorText}`);
    }
  } catch (error) {
    console.error("getEvidenceUrls error:", error);
    throw error;
  }
}

export async function getTextFileContent(issueId: string, fileName: string) {
  try {
    const response = await fetch(
      `${getApiBaseUrl()}/v1/ticket/${issueId}/files/${fileName}/text`,
      {
        method: "GET",
        headers: {
          Authorization: `${process.env.REACT_APP_LEGAL_API_KEY}`
        }
      }
    );
    if (!response.ok) throw new Error('Failed to fetch file');

    return await response.text();
  } catch (error) {
    console.error('Error fetching text file:', error);
    return null;
  }
}

export async function uploadFile(issueId: string, fileDetail: FileUploadDetails, file: FileUploadFile) {
  try {
    const formData = new FormData();
    formData.append('fileUploadDetails', JSON.stringify(fileDetail));
    formData.append('file', file, fileDetail.originalFileName);

    const header = {
      ...getDefaultHeader("PUT"),
      body: formData as unknown as BodyInit,
    };

    const response = await fetch(`${getApiBaseUrl()}/evidenceList/${issueId}/files`, header);

    if (!response.ok) {
      throw new Error(`Failed to upload files. Status: ${response.status}.`);
    }

    console.log("Successfully uploaded %s", fileDetail.originalFileName);
    return response.json();
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function deleteFile(issueId: string, fileKey: string) {
  try {
    const header = getDefaultHeader("DELETE");
    const response = await fetch(
      `${getApiBaseUrl()}/evidenceList/${issueId}/files/${fileKey}/tags`, header);

    if (response.status === 200) {
      return response.json();
    } else if (response.status === 401) {
      throw new Error("Unauthorized");
    } else {
      const errorText = await response.text();
      throw new Error(`Unexpected error (${response.status}): ${errorText}`);
    }
  } catch (error) {
    console.error("deleteFile error:", error);
    throw error;
  }
}

export async function logout() {
  try {
    const response = await fetch(
      `${getApiBaseUrl()}/logout?originUrl=${window.location.origin}/`,
      getDefaultHeader("POST")
    );

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to log out. Status: ${response.status}. Detail: ${error}`);
    }

    return;
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function getAuditLogs(issueId: string) {
  try {
    const response = await fetch(
      `${getApiBaseUrl()}/ticket/${issueId}/auditLogs`, getDefaultHeader("GET"));

    if (response.status === 200) {
      return response.json();
    } else if (response.status === 401) {
      throw new Error("Unauthorized");
    } else {
      const errorText = await response.text();
      throw new Error(`Unexpected error (${response.status}): ${errorText}`);
    }
  } catch (error) {
    console.error("getAuditLogs error:", error);
    throw error;
  }
}
