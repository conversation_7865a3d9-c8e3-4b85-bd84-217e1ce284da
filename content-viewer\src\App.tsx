import {
  Route,
  Routes,
  BrowserRouter,
} from "react-router-dom";
import Home from "./home";
import EvidenceHome from "./evidenceHome";
import { FilePresignProvider } from "./filePresignContext";
import { AuthProvider } from "./authContext";
import Banner from "./banner";
import { RootAppContainerStyle } from "./style";

const App = () => {
  return (
    <div style={{ backgroundColor: '#E9E9E9', minHeight: '100vh', minWidth: '100vw' }}>
      <AuthProvider>
        <Banner />
        <div style={RootAppContainerStyle}>
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/evidenceList/:issueId" element={<FilePresignProvider><EvidenceHome /></FilePresignProvider>} />
          </Routes>
        </div>
      </AuthProvider>
    </div>
  );
}

const AppWithRouterAccess = () => (
  <BrowserRouter>
    <App />
  </BrowserRouter>
);

export default AppWithRouterAccess;
