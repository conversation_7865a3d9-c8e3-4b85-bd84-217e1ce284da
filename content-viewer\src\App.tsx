import {
  Route,
  Routes,
  BrowserRouter,
} from "react-router-dom";
import Home from "./home";
import EvidenceHome from "./evidenceHome";
import { FilePresignProvider } from "./filePresignContext";

const App = () => {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/evidenceList/:issueId" element={<FilePresignProvider><EvidenceHome /></FilePresignProvider>} />
    </Routes>
  );
}

const AppWithRouterAccess = () => (
  <BrowserRouter>
    <App />
  </BrowserRouter>
);

export default AppWithRouterAccess;
