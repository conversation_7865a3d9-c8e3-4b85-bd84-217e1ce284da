{"name": "api-service", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "node server", "local": "set IS_LOCAL=true && node server"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-dynamodb": "^3.828.0", "@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/lib-dynamodb": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@codegenie/serverless-express": "^4.16.0", "@vendia/serverless-express": "^4.12.6", "aws-cloudfront-sign": "^3.0.2", "cors": "^2.8.5", "express": "^5.1.0"}}