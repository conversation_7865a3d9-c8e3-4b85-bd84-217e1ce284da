import React, { useEffect, useState } from 'react';
import ForgeRecon<PERSON><PERSON>, { useProductContext, Text, Checkbox, Textfield, Label, Select } from '@forge/react'; // Removed 'Select' as it's not used
import { invoke } from '@forge/bridge';

const typeOptions = [
  { label: 'Web Page Incident', value: 'webPage' },
  { label: 'Chat Im Incident', value: 'chat' },
  { label: 'Online Game Incident', value: 'onlineGame' },
]

const App = () => {
  const [internetIncidentType, setInternetIncidentType] = useState(null);

  const [url, setUrl] = useState('');
  const [thirdParty, setThirdParty] = useState(false);
  const [pageAdditionalInfo, setPageAdditionalInfo] = useState('');

  const [chatClient, setChatClient] = useState('');
  const [chatRoomName, setChatRoomName] = useState('');
  const [chatContent, setChatContent] = useState('');
  const [chatAdditionalInfo, setChatAdditionalInfo] = useState('');

  const [console, setConsole] = useState('')
  const [gameName, setGameName] = useState('')
  const [gameContent, setGameContent] = useState('')
  const [gameAdditionalInfo, setGameAdditionalInfo] = useState('')
  const [isDataReady, setIsDataReady] = useState(false);

  const context = useProductContext();

  const onChangeInternetIncidentType = (e) => {

    if (context == undefined) return;
    const { key } = context.extension.issue;
    setInternetIncidentType(e);
    
    if (e.value === 'webPage') {
      setChatClient('');
      invoke('setChatClient', { cloudId: context.cloudId, issueId: key, chatClient: '' });
      setChatRoomName('');
      invoke('setChatRoomName', { cloudId: context.cloudId, issueId: key, chatRoomName: '' });
      setChatContent('');
      invoke('setContent', { cloudId: context.cloudId, issueId: key, content: '' });
      setChatAdditionalInfo('');
      invoke('setChatAdditionalInfo', { cloudId: context.cloudId, issueId: key, chatAdditionalInfo: '' });
      setConsole('');
      invoke('setConsole', { cloudId: context.cloudId, issueId: key, console: '' });
      setGameName('');
      invoke('setGameName', { cloudId: context.cloudId, issueId: key, gameName: '' });
      setGameContent('');
      invoke('setOnlineGameContent', { cloudId: context.cloudId, issueId: key, content: '' });
      setGameAdditionalInfo('');
      invoke('setOnlineGameAdditionalInfo', { cloudId: context.cloudId, issueId: key, additionalInfo: '' });
    } else if (e.value === 'chat') {
      setUrl('');
      invoke('setUrl', { cloudId: context.cloudId, issueId: key, url: '' });
      setThirdParty(false);
      invoke('setThirdParty', { cloudId: context.cloudId, issueId: key, thirdParty: false });
      setPageAdditionalInfo('');
      invoke('setPageAdditionalInfo', { cloudId: context.cloudId, issueId: key, pageAdditionalInfo: '' });
      setConsole('');
      invoke('setConsole', { cloudId: context.cloudId, issueId: key, console: '' });
      setGameName('');
      invoke('setGameName', { cloudId: context.cloudId, issueId: key, gameName: '' });
      setGameContent('');
      invoke('setOnlineGameContent', { cloudId: context.cloudId, issueId: key, content: '' });
      setGameAdditionalInfo('');
      invoke('setOnlineGameAdditionalInfo', { cloudId: context.cloudId, issueId: key, additionalInfo: '' });
    } else if (e.value === 'onlineGame') {
      setUrl('');
      invoke('setUrl', { cloudId: context.cloudId, issueId: key, url: '' });
      setThirdParty(false);
      invoke('setThirdParty', { cloudId: context.cloudId, issueId: key, thirdParty: false });
      setPageAdditionalInfo('');
      invoke('setPageAdditionalInfo', { cloudId: context.cloudId, issueId: key, pageAdditionalInfo: '' });
      setChatClient('');
      invoke('setChatClient', { cloudId: context.cloudId, issueId: key, chatClient: '' });
      setChatRoomName('');
      invoke('setChatRoomName', { cloudId: context.cloudId, issueId: key, chatRoomName: '' });
      setChatContent('');
      invoke('setContent', { cloudId: context.cloudId, issueId: key, content: '' });
      setChatAdditionalInfo('');
      invoke('setChatAdditionalInfo', { cloudId: context.cloudId, issueId: key, chatAdditionalInfo: '' });
    }

    invoke('setInternetIncidentType', { cloudId: context.cloudId, issueId: key, type: e });
  }

  const onChangeUrl = (e) => {
    setUrl(e.target.value);
  };

  const onBlurUrl = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setUrl', { cloudId: context.cloudId, issueId: key, url: url });
  };

  const onChangeThirdParty = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    setThirdParty(e.target.checked);
    invoke('setThirdParty', { cloudId: context.cloudId, issueId: key, thirdParty: e.target.checked });
  };

  const onChangeChatClient = (e) => {
    setChatClient(e.target.value);
  };

  const onBlurChatClient = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setChatClient', { cloudId: context.cloudId, issueId: key, chatClient: chatClient });
  };

  const onChangeChatRoomName = (e) => {
    setChatRoomName(e.target.value);
  };

  const onBlurChatRoomName = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setChatRoomName', { cloudId: context.cloudId, issueId: key, chatRoomName: chatRoomName });
  };

  const onChangeChatContent = (e) => {
    setChatContent(e.target.value);
  };

  const onBlurContent = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setContent', { cloudId: context.cloudId, issueId: key, content: chatContent });
  };

  const onChangePageAdditionalInfo = (e) => {
    setPageAdditionalInfo(e.target.value);
  };

  const onBlurPageAdditionalInfo = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setPageAdditionalInfo', { cloudId: context.cloudId, issueId: key, pageAdditionalInfo: pageAdditionalInfo });
  };

  const onChangeChatAdditionalInfo = (e) => {
    setChatAdditionalInfo(e.target.value);
  };

  const onBlurChatAdditionalInfo = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setChatAdditionalInfo', { cloudId: context.cloudId, issueId: key, chatAdditionalInfo: chatAdditionalInfo });
  };

  const onChangeConsole = (e) => {
    setConsole(e.target.value);
  }

  const onBlurConsole = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setConsole', { cloudId: context.cloudId, issueId: key, console: e.target.value });
  }

  const onChangeGameName = (e) => {
    setGameName(e.target.value);
  }

  const onBlurGameName = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setGameName', { cloudId: context.cloudId, issueId: key, gameName: e.target.value });
  }

  const onChangeGameContent = (e) => {
    setGameContent(e.target.value);
  }

  const onBlurGameContent = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setOnlineGameContent', { cloudId: context.cloudId, issueId: key, content: e.target.value });
  }

  const onChangeGameAdditionalInfo = (e) => {
    setGameAdditionalInfo(e.target.value);
  }

  const onBlurGameAdditionalInfo = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setOnlineGameAdditionalInfo', { cloudId: context.cloudId, issueId: key, additionalInfo: e.target.value });
  }

  useEffect(() => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;

    const fetchData = async () => {
      try {
        const { key } = context.extension.issue;

        const [
          incidentType,
          fetchedUrl,
          fetchedThirdParty,
          fetchedChatClient,
          fetchedChatRoomName,
          fetchedContent,
          fetchedPageAdditionalInfo,
          fetchedChatAdditionalInfo,
          console,
          gameName,
          gameContent,
          additionalInfo,
        ] = await Promise.all([
          invoke('getInternetIncidentType', { issueId: key }),
          invoke('getUrl', { issueId: key }),
          invoke('getThirdParty', { issueId: key }),
          invoke('getChatClient', { issueId: key }),
          invoke('getChatRoomName', { issueId: key }),
          invoke('getContent', { issueId: key }),
          invoke('getPageAdditionalInfo', { issueId: key }),
          invoke('getChatAdditionalInfo', { issueId: key }),
          invoke('getConsole', { issueId: key }),
          invoke('getGameName', { issueId: key }),
          invoke('getOnlineGameContent', { issueId: key }),
          invoke('getOnlineGameAdditionalInfo', { issueId: key }),
        ]);

        setInternetIncidentType(JSON.parse(incidentType));
        setUrl(fetchedUrl);
        setThirdParty(fetchedThirdParty);
        setChatClient(fetchedChatClient);
        setChatRoomName(fetchedChatRoomName);
        setChatContent(fetchedContent);
        setPageAdditionalInfo(fetchedPageAdditionalInfo);
        setChatAdditionalInfo(fetchedChatAdditionalInfo);
        setConsole(console);
        setGameName(gameName);
        setGameContent(gameContent);
        setGameAdditionalInfo(additionalInfo);

        setIsDataReady(true);
      } catch (error) {
        console.error('Failed to fetch issue data:', error);
        setIsDataReady(true);
      }
    };

    fetchData();
  }, [context]);

  const getFields = () => {
    if (internetIncidentType?.value === 'webPage') {
      return (
        <>
          <Label labelFor="url">Web Page URL</Label>
          <Textfield id="url" type='url' value={url} onChange={onChangeUrl} onBlur={onBlurUrl} maxLength={255} />

          <Label labelFor="thirdParty">Third Party Hosted Content?</Label>
          <Checkbox id="thirdParty" isChecked={thirdParty} onChange={onChangeThirdParty} />

          <Label labelFor="pageAdditionalInfo">Web Page Additional Info</Label>
          <Textfield id="pageAdditionalInfo" value={pageAdditionalInfo} onChange={onChangePageAdditionalInfo} onBlur={onBlurPageAdditionalInfo} />
        </>
      );
    } else if (internetIncidentType?.value === 'chat') {
      return (
        <>
          <Label labelFor="chatClient">Chat Client</Label>
          <Textfield id="chatClient" value={chatClient} onChange={onChangeChatClient} onBlur={onBlurChatClient} maxLength={255} />

          <Label labelFor="chatRoomName">Chat Room Name</Label> {/* Corrected label and id */}
          <Textfield id="chatRoomName" value={chatRoomName} onChange={onChangeChatRoomName} onBlur={onBlurChatRoomName} maxLength={255} />

          <Label labelFor="content">Chat Message/Chat</Label>
          <Textfield id="content" value={chatContent} onChange={onChangeChatContent} onBlur={onBlurContent} />

          <Label labelFor="chatAdditionalInfo">Chat Specific Additional Info</Label>
          <Textfield id="chatAdditionalInfo" value={chatAdditionalInfo} onChange={onChangeChatAdditionalInfo} onBlur={onBlurChatAdditionalInfo} />
        </>
      );
    } else if (internetIncidentType?.value === 'onlineGame') {
      return (
        <>
          <Label labelFor="console">Game Console</Label>
          <Textfield id="console" value={console} onChange={onChangeConsole} onBlur={onBlurConsole} maxLength={255} />
          <Label labelFor="GameName">Game Name</Label>
          <Textfield id="GameName" value={gameName} onChange={onChangeGameName} onBlur={onBlurGameName} maxLength={255} />
          <Label labelFor="gameContent">Game Content</Label>
          <Textfield id="gameContent" value={gameContent} onChange={onChangeGameContent} onBlur={onBlurGameContent} />
          <Label labelFor="gameAdditionalInfo">Additional Info</Label>
          <Textfield id="gameAdditionalInfo" value={gameAdditionalInfo} onChange={onChangeGameAdditionalInfo} onBlur={onBlurGameAdditionalInfo} />
        </>
      )
    } else {
      return (
        <></>
      );
    }
  }

  return (
    <>
      {!isDataReady ? (
        <Text>Loading data…</Text>
      ) : (
        <>
          <Label labelFor="internetIncidentType">Internet Incident Type</Label>
          <Select id="internetIncidentType" value={internetIncidentType} options={typeOptions} onChange={onChangeInternetIncidentType} />
          {getFields()}
        </>
      )}
    </>
  );
};

ForgeReconciler.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);