import React, { useEffect, useState } from 'react';
import ForgeRecon<PERSON><PERSON>, { Text, Checkbox, Textfield, Label, Select } from '@forge/react';
import { invoke, requestJira, view } from '@forge/bridge';

const typeOptions = [
  { label: 'Web Page Incident', value: 'webPage' },
  { label: 'Chat Im Incident', value: 'chat' },
  { label: 'Online Game Incident', value: 'onlineGame' },
]

const App = () => {
  const [internetIncidentType, setInternetIncidentType] = useState(null);

  const [url, setUrl] = useState('');
  const [thirdParty, setThirdParty] = useState(false);
  const [pageAdditionalInfo, setPageAdditionalInfo] = useState('');

  const [chatClient, setChatClient] = useState('');
  const [chatRoomName, setChatRoomName] = useState('');
  const [chatContent, setChatContent] = useState('');
  const [chatAdditionalInfo, setChatAdditionalInfo] = useState('');

  const [console, setConsole] = useState('')
  const [gameName, setGameName] = useState('')
  const [gameContent, setGameContent] = useState('')
  const [gameAdditionalInfo, setGameAdditionalInfo] = useState('')
  const [isDataReady, setIsDataReady] = useState(false);

  const productContextRef = React.useRef(null);

  const onChangeInternetIncidentType = (e) => {
    if (productContextRef.current == undefined) return;
    setInternetIncidentType(e);

    if (e.value === 'webPage') {
      setChatClient('');
      invoke('setChatClient', { context: productContextRef.current, chatClient: '' });
      setChatRoomName('');
      invoke('setChatRoomName', { context: productContextRef.current, chatRoomName: '' });
      setChatContent('');
      invoke('setContent', { context: productContextRef.current, content: '' });
      setChatAdditionalInfo('');
      invoke('setChatAdditionalInfo', { context: productContextRef.current, chatAdditionalInfo: '' });
      setConsole('');
      invoke('setConsole', { context: productContextRef.current, console: '' });
      setGameName('');
      invoke('setGameName', { context: productContextRef.current, gameName: '' });
      setGameContent('');
      invoke('setOnlineGameContent', { context: productContextRef.current, content: '' });
      setGameAdditionalInfo('');
      invoke('setOnlineGameAdditionalInfo', { context: productContextRef.current, additionalInfo: '' });
    } else if (e.value === 'chat') {
      setUrl('');
      invoke('setUrl', { context: productContextRef.current, url: '' });
      setThirdParty(false);
      invoke('setThirdParty', { context: productContextRef.current, thirdParty: false });
      setPageAdditionalInfo('');
      invoke('setPageAdditionalInfo', { context: productContextRef.current, pageAdditionalInfo: '' });
      setConsole('');
      invoke('setConsole', { context: productContextRef.current, console: '' });
      setGameName('');
      invoke('setGameName', { context: productContextRef.current, gameName: '' });
      setGameContent('');
      invoke('setOnlineGameContent', { context: productContextRef.current, content: '' });
      setGameAdditionalInfo('');
      invoke('setOnlineGameAdditionalInfo', { context: productContextRef.current, additionalInfo: '' });
    } else if (e.value === 'onlineGame') {
      setUrl('');
      invoke('setUrl', { context: productContextRef.current, url: '' });
      setThirdParty(false);
      invoke('setThirdParty', { context: productContextRef.current, thirdParty: false });
      setPageAdditionalInfo('');
      invoke('setPageAdditionalInfo', { context: productContextRef.current, pageAdditionalInfo: '' });
      setChatClient('');
      invoke('setChatClient', { context: productContextRef.current, chatClient: '' });
      setChatRoomName('');
      invoke('setChatRoomName', { context: productContextRef.current, chatRoomName: '' });
      setChatContent('');
      invoke('setContent', { context: productContextRef.current, content: '' });
      setChatAdditionalInfo('');
      invoke('setChatAdditionalInfo', { context: productContextRef.current, chatAdditionalInfo: '' });
    }

    invoke('setInternetIncidentType', { context: productContextRef.current, type: e });
  }

  const onChangeUrl = (e) => {
    setUrl(e.target.value);
  };

  const onBlurUrl = (e) => {
    invoke('setUrl', { context: productContextRef.current, url: url });
  };

  const onChangeThirdParty = (e) => {
    setThirdParty(e.target.checked);
    invoke('setThirdParty', { context: productContextRef.current, thirdParty: e.target.checked });
  };

  const onChangeChatClient = (e) => {
    setChatClient(e.target.value);
  };

  const onBlurChatClient = (e) => {
    invoke('setChatClient', { context: productContextRef.current, chatClient: chatClient });
  };

  const onChangeChatRoomName = (e) => {
    setChatRoomName(e.target.value);
  };

  const onBlurChatRoomName = (e) => {
    invoke('setChatRoomName', { context: productContextRef.current, chatRoomName: chatRoomName });
  };

  const onChangeChatContent = (e) => {
    setChatContent(e.target.value);
  };

  const onBlurContent = (e) => {
    invoke('setContent', { context: productContextRef.current, content: chatContent });
  };

  const onChangePageAdditionalInfo = (e) => {
    setPageAdditionalInfo(e.target.value);
  };

  const onBlurPageAdditionalInfo = (e) => {
    invoke('setPageAdditionalInfo', { context: productContextRef.current, pageAdditionalInfo: pageAdditionalInfo });
  };

  const onChangeChatAdditionalInfo = (e) => {
    setChatAdditionalInfo(e.target.value);
  };

  const onBlurChatAdditionalInfo = (e) => {
    invoke('setChatAdditionalInfo', { context: productContextRef.current, chatAdditionalInfo: chatAdditionalInfo });
  };

  const onChangeConsole = (e) => {
    setConsole(e.target.value);
  }

  const onBlurConsole = (e) => {
    invoke('setConsole', { context: productContextRef.current, console: e.target.value });
  }

  const onChangeGameName = (e) => {
    setGameName(e.target.value);
  }

  const onBlurGameName = (e) => {
    invoke('setGameName', { context: productContextRef.current, gameName: e.target.value });
  }

  const onChangeGameContent = (e) => {
    setGameContent(e.target.value);
  }

  const onBlurGameContent = (e) => {
    invoke('setOnlineGameContent', { context: productContextRef.current, content: e.target.value });
  }

  const onChangeGameAdditionalInfo = (e) => {
    setGameAdditionalInfo(e.target.value);
  }

  const onBlurGameAdditionalInfo = (e) => {
    invoke('setOnlineGameAdditionalInfo', { context: productContextRef.current, additionalInfo: e.target.value });
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        const context = await view.getContext();
        if (context.accountId) {
          const res = await requestJira(`/rest/api/3/user?accountId=${context.accountId}`);
          const userData = await res.json();
          productContextRef.current = { user: userData.displayName };
        }

        productContextRef.current = {
          ...productContextRef.current,
          cloudId: context.cloudId,
          issueId: context.extension.issue.key
        };

        const [
          incidentType,
          fetchedUrl,
          fetchedThirdParty,
          fetchedChatClient,
          fetchedChatRoomName,
          fetchedContent,
          fetchedPageAdditionalInfo,
          fetchedChatAdditionalInfo,
          console,
          gameName,
          gameContent,
          additionalInfo,
        ] = await Promise.all([
          invoke('getInternetIncidentType', { issueId: productContextRef.current.issueId }),
          invoke('getUrl', { issueId: productContextRef.current.issueId }),
          invoke('getThirdParty', { issueId: productContextRef.current.issueId }),
          invoke('getChatClient', { issueId: productContextRef.current.issueId }),
          invoke('getChatRoomName', { issueId: productContextRef.current.issueId }),
          invoke('getContent', { issueId: productContextRef.current.issueId }),
          invoke('getPageAdditionalInfo', { issueId: productContextRef.current.issueId }),
          invoke('getChatAdditionalInfo', { issueId: productContextRef.current.issueId }),
          invoke('getConsole', { issueId: productContextRef.current.issueId }),
          invoke('getGameName', { issueId: productContextRef.current.issueId }),
          invoke('getOnlineGameContent', { issueId: productContextRef.current.issueId }),
          invoke('getOnlineGameAdditionalInfo', { issueId: productContextRef.current.issueId }),
        ]);

        setInternetIncidentType(incidentType ? JSON.parse(incidentType) : null);
        setUrl(fetchedUrl);
        setThirdParty(fetchedThirdParty);
        setChatClient(fetchedChatClient);
        setChatRoomName(fetchedChatRoomName);
        setChatContent(fetchedContent);
        setPageAdditionalInfo(fetchedPageAdditionalInfo);
        setChatAdditionalInfo(fetchedChatAdditionalInfo);
        setConsole(console);
        setGameName(gameName);
        setGameContent(gameContent);
        setGameAdditionalInfo(additionalInfo);
      } catch (error) {
        console.error('Failed to fetch view context:', error);
      } finally {
        setIsDataReady(true);
      }
    };

    fetchData();
  }, []);

  const getFields = () => {
    if (internetIncidentType?.value === 'webPage') {
      return (
        <>
          <Label labelFor="url">Web Page URL</Label>
          <Textfield id="url" type='url' value={url} onChange={onChangeUrl} onBlur={onBlurUrl} maxLength={255} />

          <Label labelFor="thirdParty">Third Party Hosted Content?</Label>
          <Checkbox id="thirdParty" isChecked={thirdParty} onChange={onChangeThirdParty} />

          <Label labelFor="pageAdditionalInfo">Web Page Additional Info</Label>
          <Textfield id="pageAdditionalInfo" value={pageAdditionalInfo} onChange={onChangePageAdditionalInfo} onBlur={onBlurPageAdditionalInfo} />
        </>
      );
    } else if (internetIncidentType?.value === 'chat') {
      return (
        <>
          <Label labelFor="chatClient">Chat Client</Label>
          <Textfield id="chatClient" value={chatClient} onChange={onChangeChatClient} onBlur={onBlurChatClient} maxLength={255} />

          <Label labelFor="chatRoomName">Chat Room Name</Label> {/* Corrected label and id */}
          <Textfield id="chatRoomName" value={chatRoomName} onChange={onChangeChatRoomName} onBlur={onBlurChatRoomName} maxLength={255} />

          <Label labelFor="content">Chat Message/Chat</Label>
          <Textfield id="content" value={chatContent} onChange={onChangeChatContent} onBlur={onBlurContent} />

          <Label labelFor="chatAdditionalInfo">Chat Specific Additional Info</Label>
          <Textfield id="chatAdditionalInfo" value={chatAdditionalInfo} onChange={onChangeChatAdditionalInfo} onBlur={onBlurChatAdditionalInfo} />
        </>
      );
    } else if (internetIncidentType?.value === 'onlineGame') {
      return (
        <>
          <Label labelFor="console">Game Console</Label>
          <Textfield id="console" value={console} onChange={onChangeConsole} onBlur={onBlurConsole} maxLength={255} />
          <Label labelFor="GameName">Game Name</Label>
          <Textfield id="GameName" value={gameName} onChange={onChangeGameName} onBlur={onBlurGameName} maxLength={255} />
          <Label labelFor="gameContent">Game Content</Label>
          <Textfield id="gameContent" value={gameContent} onChange={onChangeGameContent} onBlur={onBlurGameContent} />
          <Label labelFor="gameAdditionalInfo">Additional Info</Label>
          <Textfield id="gameAdditionalInfo" value={gameAdditionalInfo} onChange={onChangeGameAdditionalInfo} onBlur={onBlurGameAdditionalInfo} />
        </>
      )
    } else {
      return (
        <></>
      );
    }
  }

  return (
    <>
      {!isDataReady ? (
        <Text>Loading data…</Text>
      ) : (
        <>
          <Label labelFor="internetIncidentType">Internet Incident Type</Label>
          <Select id="internetIncidentType" value={internetIncidentType} options={typeOptions} onChange={onChangeInternetIncidentType} />
          {getFields()}
        </>
      )}
    </>
  );
};

ForgeReconciler.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);