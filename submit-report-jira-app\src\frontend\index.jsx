import React, { useEffect, useState } from 'react';
import Forge<PERSON><PERSON>on<PERSON><PERSON>, { useProductContext, TextArea  } from '@forge/react';
import { invoke } from '@forge/bridge';

const App = () => {
  const [data, setData] = useState(null);

  const context = useProductContext();

  useEffect(() => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;

    const fetchData = async () => {
      try {
        const { key } = context.extension.issue;

        const fetchedData = await invoke('getValues', { issueId: key });

        setData(JSON.stringify(fetchedData, null, 2));
      } catch (error) {
        console.error('Failed to fetch data:', error);
      }
    };

    fetchData();
  }, [context]);

  return (
    <>
      <TextArea
        id="area"
        placeholder="Loading data..."
        value={data}
        name="area"
      />
    </>
  );
};

ForgeReconciler.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
