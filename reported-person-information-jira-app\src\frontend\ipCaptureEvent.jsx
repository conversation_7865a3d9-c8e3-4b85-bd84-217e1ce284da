import React, { useState, useEffect } from 'react';
import { Label, Textfield, DatePicker, Checkbox, Select } from '@forge/react';
import { invoke } from '@forge/bridge';

const eventNames = [
  { label: 'Login', value: 'Login' },
  { label: 'Registration', value: 'Registration' },
  { label: 'Purchase', value: 'Purchase' },
  { label: 'Upload', value: 'Upload' },
  { label: 'Other', value: 'Other' },
  { label: 'Unknown', value: 'Unknown' },
]

export const IpCaptureEventFields = ({ context }) => {
  const [ipAddress, setIpAddress] = useState('');
  const [eventName, setEventName] = useState('');
  const [dateTime, setDateTime] = useState(new Date().toISOString());
  const [possibleProxy, setPossibleProxy] = useState(false);
  const [port, setPort] = useState('');

  const onChangeIpAddress = (e) => {
    setIpAddress(e.target.value);
  };

  const onBlurIpAddress = (e) => {
    invoke('setIpAddress', { context, ipAddress: e.target.value });
  };

  const onChangeEventName = (e) => {
    setEventName(e);
    invoke('setEventName', { context, eventName: e });
  };

  const onChangeDateTime = (e) => {
    setDateTime(e.target.value);
  };

  const onBlurDateTime = (e) => {
    invoke('setIpEventDateTime', { context, dateTime: e.target.value });
  };


  const onChangePossibleProxy = (e) => {
    const value = e.target.checked;
    setPossibleProxy(value);
    invoke('setPossibleProxy', { context, possibleProxy: value });
  };

  const onChangePort = (e) => {
    setPort(e.target.value);
  };

  const onBlurPort = (e) => {
    invoke('setPort', { context, port: e.target.value });
  };

  useEffect(() => {
    // Data will be populated via populateFieldsFromDDB function from parent component
    // Individual getter functions have been removed in favor of a single getReport function
  }, [context]);

  return (
    <>
      <Label labelFor="ipAddress">IP Address</Label>
      <Textfield id="ipAddress" value={ipAddress} onChange={onChangeIpAddress} onBlur={onBlurIpAddress}/>
      <Label labelFor="eventName">Event Name</Label>
      <Select id="eventName" options={eventNames} value={eventName} onChange={onChangeEventName} />
      <Label labelFor="dateTime">IP Event Time</Label>
      <Textfield type='datetime-local' id="dateTime" value={dateTime} onChange={onChangeDateTime} onBlur={onBlurDateTime} />
      <Label labelFor="possibleProxy">Possible Proxy</Label>
      <Checkbox id="possibleProxy" isChecked={possibleProxy} onChange={onChangePossibleProxy} />
      <Label labelFor="port">Port</Label>
      <Textfield id="port" type='number' value={port} onChange={onChangePort} onBlur={onBlurPort} min={1} max={65535}/>
    </>
  );
}