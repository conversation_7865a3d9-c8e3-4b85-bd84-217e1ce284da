import Resolver from '@forge/resolver';
import { kvs } from '@forge/kvs';

const resolver = new Resolver();

async function getHelper(key) {
  const storedValue = await kvs.get(key);
  return storedValue || '';
}

async function setHelper(key, context, value) {
  await kvs.set(key, value);

  const body = {
    fieldName: key,
    fieldValue: value,
    user: context.user
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/ticket/${context.issueId}/customFieldUpdate/auditLogs`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setHelper. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setHelper. Error: ${error}`);
  }
}

resolver.define('setReporterFirstName', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-first-name`;
  return setHelper(key, payload.context, payload.firstName).catch(err => console.error("Failed at setReporterFirstName", err));
});

resolver.define('getReporterFirstName', ({ payload }) => {
  return getHelper(`${payload.issueId}-reporter-first-name`);
});

  resolver.define('setReporterLastName', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-last-name`;
  return setHelper(key, payload.context, payload.lastName).catch(err => console.error("Failed at setReporterLastName", err));
});

resolver.define('getReporterLastName', ({ payload }) => {
  return getHelper(`${payload.issueId}-reporter-last-name`);
});

resolver.define('setReporterPhone', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-phone`;
  return setHelper(key, payload.context, payload.phone).catch(err => console.error("Failed at setReporterPhone", err));
});

resolver.define('getReporterPhone', ({ payload }) => {
  return getHelper(`${payload.issueId}-reporter-phone`);
});

resolver.define('setReporterEmail', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-email`;
  return setHelper(key, payload.context, payload.email).catch(err => console.error("Failed at setReporterEmail", err));
});

resolver.define('getReporterEmail', ({ payload }) => {
  return getHelper(`${payload.issueId}-reporter-email`);
});

resolver.define('setReporterAge', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-age`;
  return setHelper(key, payload.context, payload.age).catch(err => console.error("Failed at setReporterAge", err));
});

resolver.define('getReporterAge', ({ payload }) => {
  return getHelper(`${payload.issueId}-reporter-age`);
});

resolver.define('setReporterDob', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-date-of-birth`;
  return setHelper(key, payload.context, payload.dob).catch(err => console.error("Failed at setReporterDob", err));
});

resolver.define('getReporterDob', ({ payload }) => {
  return getHelper(`${payload.issueId}-reporter-date-of-birth`);
});

resolver.define('setCompanyTemplate', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-company-template`;
  return setHelper(key, payload.context, payload.template).catch(err => console.error("Failed at setCompanyTemplate", err));
});

resolver.define('getCompanyTemplate', ({ payload }) => {
  return getHelper(`${payload.issueId}-reporter-company-template`);
});

resolver.define('setTerm', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-term-of-service`;
  return setHelper(key, payload.context, payload.term).catch(err => console.error("Failed at setTerm", err));
});

resolver.define('getTerm', ({ payload }) => {
  return getHelper(`${payload.issueId}-reporter-term-of-service`);
});

resolver.define('setUrl', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-legal-url`;
  return setHelper(key, payload.context, payload.url).catch(err => console.error("Failed at setUrl", err));
});

resolver.define('getUrl', ({ payload }) => {
  return getHelper(`${payload.issueId}-reporter-legal-url`);
});

export const handler = resolver.getDefinitions();
