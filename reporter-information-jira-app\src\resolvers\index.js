import Resolver from '@forge/resolver';

const resolver = new Resolver();

async function setHelper(key, context, value) {
  const body = {
    fieldName: key,
    fieldValue: value,
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/ticket/${context.issueId}/customFieldUpdate/auditLogs`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setInDDB. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setInDDB. Error: ${error}`);
    throw error;
  }
}

resolver.define('setReporterFirstName', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-first-name`;
  return setHelper(key, payload.context, payload.firstName).catch(err => console.error("Failed at setReporterFirstName", err));
});

resolver.define('setReporterLastName', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-last-name`;
  return setHelper(key, payload.context, payload.lastName).catch(err => console.error("Failed at setReporterLastName", err));
});

resolver.define('setReporterPhone', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-phone`;
  return setHelper(key, payload.context, payload.phone).catch(err => console.error("Failed at setReporterPhone", err));
});

resolver.define('setReporterEmail', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-email`;
  return setHelper(key, payload.context, payload.email).catch(err => console.error("Failed at setReporterEmail", err));
});

resolver.define('setReporterAge', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-age`;
  return setHelper(key, payload.context, `${payload.age}`).catch(err => console.error("Failed at setReporterAge", err));
});

resolver.define('setReporterDob', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-date-of-birth`;
  return setHelper(key, payload.context, payload.dateOfBirth).catch(err => console.error("Failed at setReporterDob", err));
});

resolver.define('setCompanyTemplate', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-company-template`;
  return setHelper(key, payload.context, payload.companyTemplate).catch(err => console.error("Failed at setCompanyTemplate", err));
});

resolver.define('setTerm', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-term-of-service`;
  return setHelper(key, payload.context, payload.termOfService).catch(err => console.error("Failed at setTerm", err));
});

resolver.define('setUrl', ({ payload }) => {
  const key = `${payload.context.issueId}-reporter-legal-url`;
  return setHelper(key, payload.context, payload.legalUrl).catch(err => console.error("Failed at setUrl", err));
});

async function getReport(issueId) {  
  try {
    const response = await fetch(`${process.env.API_HOST}/v1/ticket/${issueId}/reportFields`, {
      method: "GET",
      headers: {
        "Authorization": process.env.API_KEY,
        "Content-Type": "application/json"
      }
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to getReport. Status: ${response.status}. Detail: ${error}`);
    }

    return response.json();
  } catch (error) {
    console.error(`Failed to getReport. Error: ${error}`);
    throw error;
  }
}

resolver.define('getReport', ({ payload }) => {
  return getReport(payload.issueId);
});

export const handler = resolver.getDefinitions();
