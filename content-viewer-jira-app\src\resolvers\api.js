const defaultHeader = {
  method: "GET",
  headers: {
    "Authorization": process.env.API_KEY
  }
}

export async function getEvidenceUrls(issueId, allowedExtensions) {
  return await fetch(`${process.env.API_HOST}/v1/ticket/${issueId}/files?includeFileExtensions=${allowedExtensions}`, defaultHeader)
    .then(async (response) => {
      if (!response.ok) {
        const error = await response.text();
        throw new Error(`Failed to getEvidenceUrls. Status: ${response.status}. Detail: ${error}`);
      }
      return response.json();
    })
    .catch((error) => console.error(`Failed to getEvidenceUrls. Error: ${error}`));
}

export async function getTextFileContent(issueId, key) {
  return await fetch(`${process.env.API_HOST}/v1/ticket/${issueId}/files/${key}/text`, defaultHeader)
    .then(async (response) => {
      if (!response.ok) {
        const error = await response.text();
        throw new Error(`Failed to getTextFileContent. Status: ${response.status}. Detail: ${error}`);
      }
      return response.text();
    })
    .catch((error) => console.error(`Failed to getTextFileContent. Error: ${error}`));
}
