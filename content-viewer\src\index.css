body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

html, body, #root {
  height: 100%;
  margin: 0;
}

.p-tabview-nav {
  background-color: #E9E9E9;
  
}

.p-tabview-nav-link {
  background-color: #E9E9E9;
}

.p-tabview-selected .p-tabview-nav-link {
  border-bottom: 2px solid black;
  background-color: #E9E9E9;
}

.p-datatable-thead {
  background-color: #AEAEAE;
}