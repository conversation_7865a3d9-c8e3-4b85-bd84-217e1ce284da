import Resolver from '@forge/resolver';
import { getEvidenceUrls, getTextFileContent } from "./api";

const resolver = new Resolver();

resolver.define("getEvidenceUrls", (req) => {
  if (req.payload && req.payload.issueId && req.payload.allowedFileExtensions) {
    const allowedExtensions = req.payload.allowedFileExtensions.join(',');
    return getEvidenceUrls(req.payload.issueId, allowedExtensions)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        console.error(error);
        return [];
      });
  }
});

resolver.define("getTextFileContent", (req) => {
  if (req.payload && req.payload.issueId && req.payload.key) {
    return getTextFileContent(req.payload.issueId, req.payload.key)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        console.error(error);
        return "";
      });
  }
});

resolver.define('getEnv', (req) => {
  return process.env
});

resolver.define('downloadFile', (req) => {
  if (req.payload && req.payload.url && req.payload.fileName) {
    return {
      body: {
        fileName: req.payload.fileName,
        url: req.payload.url,
      },
    };
  }
});

export const handler = resolver.getDefinitions();
