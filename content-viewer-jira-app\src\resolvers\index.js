import Resolver from '@forge/resolver';
import { getEvidenceUrls, getTextFileContent } from "./api";
import api, { route } from '@forge/api';

const resolver = new Resolver();

resolver.define("getEvidenceUrls", (req) => {
  if (req.payload && req.payload.issueId) {
    return getEvidenceUrls(req.payload.issueId)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        console.error(error);
        return [];
      });
  }
});

resolver.define("getTextFileContent", (req) => {
  if (req.payload && req.payload.issueId && req.payload.key) {
    return getTextFileContent(req.payload.issueId, req.payload.key)
      .then((response) => {
        return response;
      })
      .catch((error) => {
        console.error(error);
        return "";
      });
  }
});

resolver.define('getEnv', (req) => {
  return process.env
});

resolver.define('downloadFile', (req) => {
  if (req.payload && req.payload.url && req.payload.fileName) {
    return {
      body: {
        fileName: req.payload.fileName,
        url: req.payload.url,
      },
    };
  }
});

resolver.define('getUserEmail', async () => {
  const res = await api.asUser().requestJira(route`/rest/api/3/myself`);
  const data = await res.json();
  return data.emailAddress;
});

export const handler = resolver.getDefinitions();
