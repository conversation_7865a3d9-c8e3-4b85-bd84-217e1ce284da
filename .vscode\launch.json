{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug API server",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "program": "${workspaceFolder}\\api-service\\server.js",
      "env": {
        "IS_LOCAL": "true",
        "FILE_HOST": "https://d1obsu2ea762bp.cloudfront.net",
        "S3_BUCKET": "t2gp-social-develop",
        "S3_BUCKET_FOLDER": "legaltest/rs",
        "SSO_PROFILE": "stevej",
        "DDB_REGION": "us-west-2",
        "DYNAMO_TABLE_NAME": "ReportChildAbuseJIRACustomFields"
      }
    },
    {
      "type": "chrome",                   // or "edge"
      "request": "launch",
      "name": "React: Debug in Chrome",
      "url": "http://localhost:3000",     // update if your dev server runs elsewhere
      "webRoot": "${workspaceFolder}/content-viewer/src",
      "sourceMaps": true
    }
  ]
}