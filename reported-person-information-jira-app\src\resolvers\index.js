import Resolver from '@forge/resolver';
import { kvs } from '@forge/kvs';

const resolver = new Resolver();

async function getHelper(key) {
  const storedValue = await kvs.get(key);
  return storedValue || '';
}

async function setHelper(key, context, value) {
  await kvs.set(key, value);

  const body = {
    fieldName: key,
    fieldValue: value,
    user: context.user
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/ticket/${context.issueId}/customFieldUpdate/auditLogs`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setHelper. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setHelper. Error: ${error}`);
  }
}

async function setObjectHelper(key, context, object, stringToPersist) {
  await kvs.set(key, JSON.stringify(object));

  const body = {
    fieldName: key,
    fieldValue: stringToPersist,
    user: context.user
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/ticket/${context.issueId}/customFieldUpdate/auditLogs`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setObjectHelper. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setObjectHelper. Error: ${error}`);
  }
}

resolver.define('setFirstName', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-first-name`;
  return setHelper(key, payload.context, payload.firstName).catch(err => console.error("Failed at setFirstName", err));
});

resolver.define('setLastName', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-last-name`;
  return setHelper(key, payload.context, payload.lastName).catch(err => console.error("Failed at setLastName", err));
});

resolver.define('setPhone', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-phone`;
  return setHelper(key, payload.context, payload.phone).catch(err => console.error("Failed at setPhone", err));
});

resolver.define('setEmail', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-email`;
  return setHelper(key, payload.context, payload.email).catch(err => console.error("Failed at setEmail", err));
});

resolver.define('setAge', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-age`;
  return setHelper(key, payload.context, payload.age).catch(err => console.error("Failed at setAge", err));
});

resolver.define('setDateOfBirth', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-dob`;
  return setHelper(key, payload.context, payload.dob).catch(err => console.error("Failed at setDateOfBirth", err));
});

resolver.define('setStreet', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-street`;
  return setHelper(key, payload.context, payload.street).catch(err => console.error("Failed at setStreet", err));
});

resolver.define('setCity', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-city`;
  return setHelper(key, payload.context, payload.city).catch(err => console.error("Failed at setCity", err));
});

resolver.define('setZipCode', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-zip-code`;
  return setHelper(key, payload.context, payload.zipCode).catch(err => console.error("Failed at setZipCode", err));
});

resolver.define('setState', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-state`;
  return setObjectHelper(key, payload.context, payload.state, payload.state.value).catch(err => console.error("Failed at setState", err));
});

resolver.define('setNonUsaState', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-non-usa-state`;
  return setHelper(key, payload.context, payload.nonUsaState).catch(err => console.error("Failed at setNonUsaState", err));
});

resolver.define('setCountry', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-country`;
  return setObjectHelper(key, payload.context, payload.country, payload.country.value).catch(err => console.error("Failed at setCountry", err));
});

resolver.define('setAddressType', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-address-type`;
  return setObjectHelper(key, payload.context, payload.type, payload.type.value).catch(err => console.error("Failed at setType", err));
});

resolver.define('setVehicleDescription', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-vehicle-description`;
  return setHelper(key, payload.context, payload.vehicleDescription).catch(err => console.error("Failed at setVehicleDescription", err));
});

resolver.define('setEspService', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-esp-service`;
  return setHelper(key, payload.context, payload.espService).catch(err => console.error("Failed at setEspService", err));
});

resolver.define('setScreenName', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-screen-name`;
  return setHelper(key, payload.context, payload.screenName).catch(err => console.error("Failed at setScreenName", err));
});

resolver.define('setDisplayName', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-display-name`;
  return setHelper(key, payload.context, payload.displayName).catch(err => console.error("Failed at setDisplayName", err));
});

resolver.define('setProfileUrl', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-profile-url`;
  return setHelper(key, payload.context, payload.profileUrl).catch(err => console.error("Failed at setProfileUrl", err));
});

resolver.define('setProfileBio', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-profile-bio`;
  return setHelper(key, payload.context, payload.profileBio).catch(err => console.error("Failed at setProfileBio", err));
});

resolver.define('setCompromisedAccount', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-compromised-account`;
  return setHelper(key, payload.context, payload.compromisedAccount).catch(err => console.error("Failed at setCompromisedAccount", err));
});

resolver.define('setGroupIdentifier', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-group-identifier`;
  return setHelper(key, payload.context, payload.groupIdentifier).catch(err => console.error("Failed at setGroupIdentifier", err));
});

resolver.define('setThirdPartyUserReported', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-third-party-user-reported`;
  return setHelper(key, payload.context, payload.thirdPartyUserReported).catch(err => console.error("Failed at setThirdPartyUserReported", err));
});

resolver.define('setPriorCTReports', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-prior-ct-reports`;
  return setHelper(key, payload.context, payload.priorCTReports).catch(err => console.error("Failed at setPriorCTReports", err));
});

resolver.define('setAdditionalInfo', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-additional-info`;
  return setHelper(key, payload.context, payload.additionalInfo).catch(err => console.error("Failed at setAdditionalInfo", err));
});

resolver.define('setEspIdentifer', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-esp-identifier`;
  return setHelper(key, payload.context, payload.espIdentifer).catch(err => console.error("Failed at setEspIdentifer", err));
});

resolver.define('setDeviceIdType', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-device-id-type`;
  return setHelper(key, payload.context, payload.idType).catch(err => console.error("Failed at setIdType", err));
});

resolver.define('setDeviceIdValue', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-device-id-value`;
  return setHelper(key, payload.context, payload.idValue).catch(err => console.error("Failed at setIdValue", err));
});

resolver.define('setDeviceEventName', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-device-event-name`;
  return setHelper(key, payload.context, payload.deviceEventName).catch(err => console.error("Failed at setDeviceEventName", err));
});

resolver.define('setDeviceDateTime', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-device-date-time`;
  return setHelper(key, payload.context, payload.deviceDateTime).catch(err => console.error("Failed at setDeviceDateTime", err));
});

resolver.define('setEstimatedLocationCity', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-estimated-location-city`;
  return setHelper(key, payload.context, payload.city).catch(err => console.error("Failed at setCity", err));
});

resolver.define('setEstimatedLocationRegion', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-estimated-location-region`;
  return setHelper(key, payload.context, payload.region).catch(err => console.error("Failed at setRegion", err));
});

resolver.define('setEstimatedLocationCountryCode', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-estimated-location-country-code`;
  return setObjectHelper(key, payload.context, payload.countryCode, payload.countryCode.value).catch(err => console.error("Failed at setCountryCode", err));
});

resolver.define('setEstimatedLocationVerified', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-estimated-location-verified`;
  return setHelper(key, payload.context, payload.verified).catch(err => console.error("Failed at setVerified", err));
});

resolver.define('setEstimatedLocationTimestamp', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-estimated-location-timestamp`;
  return setHelper(key, payload.context, payload.timestamp).catch(err => console.error("Failed at setTimestamp", err));
});

resolver.define('setIpAddress', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-ip-capture-event-ip-address`;
  return setHelper(key, payload.context, payload.ipAddress).catch(err => console.error("Failed at setIpAddress", err));
});

resolver.define('setEventName', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-ip-capture-event-event-name`;
  return setObjectHelper(key, payload.context, payload.eventName, payload.eventName.value).catch(err => console.error("Failed at setEventName", err));
});

resolver.define('setIpEventDateTime', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-ip-capture-event-date-time`;
  return setHelper(key, payload.context, payload.dateTime).catch(err => console.error("Failed at setDateTime", err));
});

resolver.define('setPossibleProxy', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-ip-capture-event-possible-proxy`;
  return setHelper(key, payload.context, payload.possibleProxy).catch(err => console.error("Failed at setPossibleProxy", err));
});

resolver.define('setPort', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-ip-capture-event-port`;
  return setHelper(key, payload.context, payload.port).catch(err => console.error("Failed at setPort", err));
});

resolver.define('setIsDisabledTemporarily', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-account-is-disabled-temporarily`;
  return setHelper(key, payload.context, payload.isDisabled).catch(err => console.error("Failed at setIsDisabled", err));
});

resolver.define('setDisabledDateTemporarily', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-account-disabled-date-temporarily`;
  return setHelper(key, payload.context, payload.disabledDate).catch(err => console.error("Failed at setDisabledDate", err));
});

resolver.define('setUserNotifiedTemporarily', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-account-user-notified-temporarily`;
  return setHelper(key, payload.context, payload.userNotified).catch(err => console.error("Failed at setUserNotified", err));
});

resolver.define('setUserNotifiedDateTemporarily', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-account-user-notified-date-temporarily`;
  return setHelper(key, payload.context, payload.userNotifiedDate).catch(err => console.error("Failed at setUserNotifiedDate", err));
});

resolver.define('setReenabledDateTemporarily', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-account-reenabled-date-temporarily`;
  return setHelper(key, payload.context, payload.reenabledDate).catch(err => console.error("Failed at setReenabledDate", err));
});

resolver.define('setIsDisabledPermanently', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-account-is-disabled-Permanently`;
  return setHelper(key, payload.context, payload.isDisabled).catch(err => console.error("Failed at setIsDisabled", err));
});

resolver.define('setDisabledDatePermanently', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-account-disabled-date-Permanently`;
  return setHelper(key, payload.context, payload.disabledDate).catch(err => console.error("Failed at setDisabledDate", err));
});

resolver.define('setUserNotifiedPermanently', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-account-user-notified-Permanently`;
  return setHelper(key, payload.context, payload.userNotified).catch(err => console.error("Failed at setUserNotified", err));
});

resolver.define('setUserNotifiedDatePermanently', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-account-user-notified-date-Permanently`;
  return setHelper(key, payload.context, payload.userNotifiedDate).catch(err => console.error("Failed at setUserNotifiedDate", err));
});

async function getReport(issueId) {  
  try {
    const response = await fetch(`${process.env.API_HOST}/v1/ticket/${issueId}/reportFields`, {
      method: "GET",
      headers: {
        "Authorization": process.env.API_KEY,
        "Content-Type": "application/json"
      }
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to getReport. Status: ${response.status}. Detail: ${error}`);
    }

    return response.json();
  } catch (error) {
    console.error(`Failed to getReport. Error: ${error}`);
    throw error;
  }
}

resolver.define('getReport', ({ payload }) => {
  return getReport(payload.issueId);
});

export const handler = resolver.getDefinitions();
