import Resolver from '@forge/resolver';
import { kvs } from '@forge/kvs';

const resolver = new Resolver();

async function getHelper(key) {
  const storedValue = await kvs.get(key);
  return storedValue || '';
}

async function setHelper(key, context, value) {
  await kvs.set(key, value);

  const body = {
    fieldName: key,
    fieldValue: value,
    user: context.user
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/ticket/${context.issueId}/customFieldUpdate/auditLogs`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setHelper. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setHelper. Error: ${error}`);
  }
}

async function setObjectHelper(key, context, object, stringToPersist) {
  await kvs.set(key, JSON.stringify(object));

  const body = {
    fieldName: key,
    fieldValue: stringToPersist,
    user: context.user
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/ticket/${context.issueId}/customFieldUpdate/auditLogs`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setObjectHelper. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setObjectHelper. Error: ${error}`);
  }
}

resolver.define('setFirstName', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-first-name`;
  return setHelper(key, payload.context, payload.firstName).catch(err => console.error("Failed at setFirstName", err));
});

resolver.define('getFirstName', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-first-name`);
});

resolver.define('setLastName', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-last-name`;
  return setHelper(key, payload.context, payload.lastName).catch(err => console.error("Failed at setLastName", err));
});

resolver.define('getLastName', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-last-name`);
});

resolver.define('setPhone', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-phone`;
  return setHelper(key, payload.context, payload.phone).catch(err => console.error("Failed at setPhone", err));
});

resolver.define('getPhone', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-phone`);
});

resolver.define('setEmail', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-email`;
  return setHelper(key, payload.context, payload.email).catch(err => console.error("Failed at setEmail", err));
});

resolver.define('getEmail', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-email`);
});

resolver.define('setAge', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-age`;
  return setHelper(key, payload.context, payload.age).catch(err => console.error("Failed at setAge", err));
});

resolver.define('getAge', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-age`);
});

resolver.define('setDateOfBirth', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-dob`;
  return setHelper(key, payload.context, payload.dob).catch(err => console.error("Failed at setDateOfBirth", err));
});

resolver.define('getDateOfBirth', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-dob`);
});

resolver.define('setStreet', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-street`;
  return setHelper(key, payload.context, payload.street).catch(err => console.error("Failed at setStreet", err));
});

resolver.define('getStreet', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-street`);
});

resolver.define('setCity', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-city`;
  return setHelper(key, payload.context, payload.city).catch(err => console.error("Failed at setCity", err));
});

resolver.define('getCity', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-city`);
});

resolver.define('setZipCode', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-zip-code`;
  return setHelper(key, payload.context, payload.zipCode).catch(err => console.error("Failed at setZipCode", err));
});

resolver.define('getZipCode', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-zip-code`);
});

resolver.define('setState', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-state`;
  return setObjectHelper(key, payload.context, payload.state, payload.state.value).catch(err => console.error("Failed at setState", err));
});

resolver.define('getState', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-state`);
});

resolver.define('setNonUsaState', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-non-usa-state`;
  return setHelper(key, payload.context, payload.nonUsaState).catch(err => console.error("Failed at setNonUsaState", err));
});

resolver.define('getNonUsaState', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-non-usa-state`);
});

resolver.define('setCountry', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-country`;
  return setObjectHelper(key, payload.context, payload.country, payload.country.value).catch(err => console.error("Failed at setCountry", err));
});

resolver.define('getCountry', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-country`);
});

resolver.define('setAddressType', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-address-type`;
  return setObjectHelper(key, payload.context, payload.type, payload.type.value).catch(err => console.error("Failed at setType", err));
});

resolver.define('getAddressType', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-address-type`);
});

resolver.define('setVehicleDescription', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-vehicle-description`;
  return setHelper(key, payload.context, payload.vehicleDescription).catch(err => console.error("Failed at setVehicleDescription", err));
});

resolver.define('getVehicleDescription', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-vehicle-description`);
});

resolver.define('setEspService', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-esp-service`;
  return setHelper(key, payload.context, payload.espService).catch(err => console.error("Failed at setEspService", err));
});

resolver.define('getEspService', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-esp-service`);
});

resolver.define('setScreenName', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-screen-name`;
  return setHelper(key, payload.context, payload.screenName).catch(err => console.error("Failed at setScreenName", err));
});

resolver.define('getScreenName', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-screen-name`);
});

resolver.define('setDisplayName', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-display-name`;
  return setHelper(key, payload.context, payload.displayName).catch(err => console.error("Failed at setDisplayName", err));
});

resolver.define('getDisplayName', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-display-name`);
});

resolver.define('setProfileUrl', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-profile-url`;
  return setHelper(key, payload.context, payload.profileUrl).catch(err => console.error("Failed at setProfileUrl", err));
});

resolver.define('getProfileUrl', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-profile-url`);
});

resolver.define('setProfileBio', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-profile-bio`;
  return setHelper(key, payload.context, payload.profileBio).catch(err => console.error("Failed at setProfileBio", err));
});

resolver.define('getProfileBio', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-profile-bio`);
});

resolver.define('setCompromisedAccount', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-compromised-account`;
  return setHelper(key, payload.context, payload.compromisedAccount).catch(err => console.error("Failed at setCompromisedAccount", err));
});

resolver.define('getCompromisedAccount', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-compromised-account`);
});

resolver.define('setGroupIdentifier', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-group-identifier`;
  return setHelper(key, payload.context, payload.groupIdentifier).catch(err => console.error("Failed at setGroupIdentifier", err));
});

resolver.define('getGroupIdentifier', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-group-identifier`);
});

resolver.define('setThirdPartyUserReported', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-third-party-user-reported`;
  return setHelper(key, payload.context, payload.thirdPartyUserReported).catch(err => console.error("Failed at setThirdPartyUserReported", err));
});

resolver.define('getThirdPartyUserReported', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-third-party-user-reported`);
});

resolver.define('setPriorCTReports', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-prior-ct-reports`;
  return setHelper(key, payload.context, payload.priorCTReports).catch(err => console.error("Failed at setPriorCTReports", err));
});

resolver.define('getPriorCTReports', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-prior-ct-reports`);
});

resolver.define('setAdditionalInfo', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-additional-info`;
  return setHelper(key, payload.context, payload.additionalInfo).catch(err => console.error("Failed at setAdditionalInfo", err));
});

resolver.define('getAdditionalInfo', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-additional-info`);
});

resolver.define('setEspIdentifer', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-esp-identifier`;
  return setHelper(key, payload.context, payload.espIdentifer).catch(err => console.error("Failed at setEspIdentifer", err));
});

resolver.define('getEspIdentifer', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-esp-identifier`);
});

resolver.define('setDeviceIdType', ({ payload }) => {
  const key = `${payload.context.issueId}-reported-person-device-id-type`;
  return setHelper(key, payload.context, payload.idType).catch(err => console.error("Failed at setIdType", err));
});

resolver.define('getDeviceIdType', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-device-id-type`);
});

resolver.define('setDeviceIdValue', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-device-id-value`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.idValue).catch(err => console.error("Failed at setIdValue", err));
});

resolver.define('getDeviceIdValue', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-device-id-value`);
});

resolver.define('setDeviceEventName', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-device-event-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.deviceEventName).catch(err => console.error("Failed at setDeviceEventName", err));
});

resolver.define('getDeviceEventName', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-device-event-name`);
});

resolver.define('setDeviceDateTime', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-device-date-time`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.deviceDateTime).catch(err => console.error("Failed at setDeviceDateTime", err));
});

resolver.define('getDeviceDateTime', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-device-date-time`);
});

resolver.define('setEstimatedLocationCity', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-estimated-location-city`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.city).catch(err => console.error("Failed at setCity", err));
});

resolver.define('getEstimatedLocationCity', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-estimated-location-city`);
});

resolver.define('setEstimatedLocationRegion', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-estimated-location-region`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.region).catch(err => console.error("Failed at setRegion", err));
});

resolver.define('getEstimatedLocationRegion', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-estimated-location-region`);
});

resolver.define('setEstimatedLocationCountryCode', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-estimated-location-country-code`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.countryCode, payload.countryCode.value).catch(err => console.error("Failed at setCountryCode", err));
});

resolver.define('getEstimatedLocationCountryCode', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-estimated-location-country-code`);
});

resolver.define('setEstimatedLocationVerified', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-estimated-location-verified`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.verified).catch(err => console.error("Failed at setVerified", err));
});

resolver.define('getEstimatedLocationVerified', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-estimated-location-verified`);
});

resolver.define('setEstimatedLocationTimestamp', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-estimated-location-timestamp`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.timestamp).catch(err => console.error("Failed at setTimestamp", err));
});

resolver.define('getEstimatedLocationTimestamp', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-estimated-location-timestamp`);
});

resolver.define('setIpAddress', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-ip-capture-event-ip-address`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.ipAddress).catch(err => console.error("Failed at setIpAddress", err));
});

resolver.define('setEventName', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-ip-capture-event-event-name`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.eventName, payload.eventName.value).catch(err => console.error("Failed at setEventName", err));
});

resolver.define('setIpEventDateTime', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-ip-capture-event-date-time`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.dateTime).catch(err => console.error("Failed at setDateTime", err));
});

resolver.define('setPossibleProxy', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-ip-capture-event-possible-proxy`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.possibleProxy).catch(err => console.error("Failed at setPossibleProxy", err));
});

resolver.define('setPort', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-ip-capture-event-port`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.port).catch(err => console.error("Failed at setPort", err));
});

resolver.define('getIpAddress', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-ip-capture-event-ip-address`);
});

resolver.define('getEventName', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-ip-capture-event-event-name`);
});

resolver.define('getIpEventDateTime', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-ip-capture-event-date-time`);
});

resolver.define('getPossibleProxy', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-ip-capture-event-possible-proxy`);
});

resolver.define('getPort', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-ip-capture-event-port`);
});

resolver.define('setIsDisabledTemporarily', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-account-is-disabled-temporarily`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.isDisabled).catch(err => console.error("Failed at setIsDisabled", err));
});

resolver.define('setDisabledDateTemporarily', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-account-disabled-date-temporarily`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.disabledDate).catch(err => console.error("Failed at setDisabledDate", err));
});

resolver.define('setUserNotifiedTemporarily', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-account-user-notified-temporarily`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.userNotified).catch(err => console.error("Failed at setUserNotified", err));
});

resolver.define('setUserNotifiedDateTemporarily', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-account-user-notified-date-temporarily`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.userNotifiedDate).catch(err => console.error("Failed at setUserNotifiedDate", err));
});

resolver.define('setReenabledDateTemporarily', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-account-reenabled-date-temporarily`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.reenabledDate).catch(err => console.error("Failed at setReenabledDate", err));
});

resolver.define('getIsDisabledTemporarily', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-account-is-disabled-temporarily`);
});

resolver.define('getDisabledDateTemporarily', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-account-disabled-date-temporarily`);
});

resolver.define('getUserNotifiedTemporarily', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-account-user-notified-temporarily`);
});

resolver.define('getUserNotifiedDateTemporarily', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-account-user-notified-date-temporarily`);
});

resolver.define('getReenabledDateTemporarily', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-account-reenabled-date-temporarily`);
});

resolver.define('setIsDisabledPermanently', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-account-is-disabled-Permanently`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.isDisabled).catch(err => console.error("Failed at setIsDisabled", err));
});

resolver.define('setDisabledDatePermanently', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-account-disabled-date-Permanently`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.disabledDate).catch(err => console.error("Failed at setDisabledDate", err));
});

resolver.define('setUserNotifiedPermanently', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-account-user-notified-Permanently`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.userNotified).catch(err => console.error("Failed at setUserNotified", err));
});

resolver.define('setUserNotifiedDatePermanently', ({ payload }) => {
  const key = `${payload.issueId}-reported-person-account-user-notified-date-Permanently`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.userNotifiedDate).catch(err => console.error("Failed at setUserNotifiedDate", err));
});

resolver.define('getIsDisabledPermanently', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-account-is-disabled-Permanently`);
});

resolver.define('getDisabledDatePermanently', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-account-disabled-date-Permanently`);
});

resolver.define('getUserNotifiedPermanently', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-account-user-notified-Permanently`);
});

resolver.define('getUserNotifiedDatePermanently', ({ payload }) => {
  return getHelper(`${payload.issueId}-reported-person-account-user-notified-date-Permanently`);
});

export const handler = resolver.getDefinitions();
