
import cors from "cors";
import express from "express";
import { getItemUrlsFromS3, getTextFileFromS3 } from "./s3.js";
import { ddbUpdate, getFromDDB } from "./dynamo.js";

const app = express();
app.use(express.json());
app.use(cors());

app.get('/evidenceList/:jiraIssueId', getItemUrlsFromS3);
app.get('/evidenceList/:jiraIssueId/:fileKey', getTextFileFromS3);
app.get('/appData/:jiraCloudId/:issueId', getFromDDB);
app.put('/appData/:jiraCloudId/:issueId', ddbUpdate);
export default app;
