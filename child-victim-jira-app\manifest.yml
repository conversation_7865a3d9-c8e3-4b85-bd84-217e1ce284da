modules:
  jira:issueContext:
    - key: child-victim-jira-app-issue-context
      resource: main
      resolver:
        function: resolver
      render: native
      title: Child Victim Information
      description: Child Victim Information
      label: Child Victim Information
  function:
    - key: resolver
      handler: index.handler
resources:
  - key: main
    path: src/frontend/index.jsx
app:
  runtime:
    name: nodejs22.x
    memoryMB: 256
    architecture: arm64
  id: ari:cloud:ecosystem::app/82bd2eef-abcd-4710-9106-7b49155859f4
permissions:
  scopes:
    - storage:app
  external:
    fetch:
      backend:
        - 'https://hg6e00eso6.execute-api.us-west-2.amazonaws.com/*'