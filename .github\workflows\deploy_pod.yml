name: Deploy Pod
on:
  push:
    branches:
      main
    tags-ignore:
      - '**' 
    paths:
      content-viewer/**
  workflow_dispatch:

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read  # This is required for actions/checkout

jobs:
  build-image:
    name: Build Docker Image
    runs-on: [t2gp-arc-linux]
    environment: develop
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: Get commit for version
        id: declare-envs
        run: |
          echo VER=${GITHUB_SHA::8} >> $GITHUB_ENV 
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.GH_AWS_ROLE }}
          role-session-name: ${{ vars.GH_AWS_SESSION }}
          aws-region: us-east-1
      - run: |
          echo //npm.pkg.github.com/:_authToken=${{secrets.SERVICE_ACCOUNT_GH_PAT}} >> content-viewer/.npmrc
      - name: Append API key to env files
        run: |
          API_KEY=$(aws secretsmanager get-secret-value \
            --secret-id /secrets/t2gp/SOCIAL_LEGAL_API_DEV_API_KEYS \
            --query SecretString \
            --output text)

          echo -e "REACT_APP_LEGAL_API_KEY=$API_KEY" >> content-viewer/.env

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - name: Build and Push docker
        uses: docker/build-push-action@v4
        with:
          context: content-viewer/.
          push: true
          tags: |
            ************.dkr.ecr.us-east-1.amazonaws.com/t2gp-social-report-evidence-viewer:${{env.VER}}
      - name: Helm Deploy
        id: helm_deploy
        uses: take-two-t2gp/app-charts-commit@v0.8
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: t2gp-non-production
          service: social-legal
          environment: develop
          helm-values: "social-report-evidence-viewer.app.tag=${{env.VER}}"
          raise-pr: false # skips 2nd gate in app-charts