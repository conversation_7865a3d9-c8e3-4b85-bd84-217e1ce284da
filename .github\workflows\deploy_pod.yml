name: Deploy Pod
on:
  push:
    branches:
      main
    paths:
      content-viewer/**
  workflow_dispatch:

permissions:
  id-token: write # This is required for requesting the JWT
  contents: read  # This is required for actions/checkout

jobs:
  build-image:
    name: Build Docker Image
    runs-on: [t2gp-arc-linux]
    environment: develop
    steps:
      - name: Check out code
        uses: actions/checkout@v4
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.GH_AWS_ROLE }}
          role-session-name: ${{ vars.GH_AWS_SESSION }}
          aws-region: us-east-1
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      - run: |
          echo //npm.pkg.github.com/:_authToken=${{secrets.SERVICE_ACCOUNT_GH_PAT}} >> content-viewer/.npmrc
      - name: Append API key to .env
        run: |
          echo >> content-viewer/.env
          echo "REACT_APP_LEGAL_API_KEY=$(aws secretsmanager get-secret-value \
            --secret-id /secrets/t2gp/SOCIAL_LEGAL_API_DEV_API_KEYS \
            --query SecretString \
            --output text)" >> content-viewer/.env
      - name: Build and Push docker
        uses: docker/build-push-action@v4
        with:
          context: content-viewer/.
          push: true
          tags: |
            ************.dkr.ecr.us-east-1.amazonaws.com/t2gp-social-report-evidence-viewer:${{ github.sha }}
      - name: Helm Deploy
        id: helm_deploy
        uses: take-two-t2gp/app-charts-commit@v0.8
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: t2gp-production
          service: t2gp-social-report-evidence-viewer
          environment: production
          helm-values: "app.tag=${{ github.sha }}"
          raise-pr: false # skips 2nd gate in app-charts