modules:
  jira:issueContext:
    - key: internet-details-jira-app-issue-context
      resource: main
      resolver:
        function: resolver
      render: native
      title: Internet Details
      description: Internet Details
      label: Internet Details
  function:
    - key: resolver
      handler: index.handler
resources:
  - key: main
    path: src/frontend/index.jsx
app:
  runtime:
    name: nodejs22.x
    memoryMB: 256
    architecture: arm64
  id: ari:cloud:ecosystem::app/b3a7ba5b-cdf3-4098-991e-4c9906a7f2d6
permissions:
  scopes:
    - storage:app
    - read:jira-user
  external:
    fetch:
      backend:
        - 'https://t2gp-social-legal-api.dev.d2dragon.net/*'