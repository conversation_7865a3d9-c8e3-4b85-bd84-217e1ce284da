import { DynamicTable, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Text, ButtonGroup } from "@forge/react";
import React from 'react';
import ViewContentModal from './viewContentModal';

const head = {
  cells: [
    {
      key: "url",
      content: "File Name",
      isSortable: true,
    },
    {
      key: "uploader",
      content: "Uploaded By",
      isSortable: true,
    },
    {
      key: "lastModified",
      content: "Last Modified",
      isSortable: true,
    },
    {
      key: "action",
      content: "Action",
      isSortable: true,
    }
  ],
};

function formatTime(isoString) {
  const date = new Date(isoString);
  return date.getUTCFullYear() + "-" +
    String(date.getUTCMonth() + 1).padStart(2, '0') + "-" +
    String(date.getUTCDate()).padStart(2, '0') + " " +
    String(date.getUTCHours()).padStart(2, '0') + ":" +
    String(date.getUTCMinutes()).padStart(2, '0') + ":" +
    String(date.getUTCSeconds()).padStart(2, '0');
}

function EvidenceTable({ issueId, files }) {
  const [rows, setRows] = React.useState([]);
  const [isViewContentModalOpen, setIsViewContentModalOpen] = React.useState(false);

  const selectedContentRef = React.useRef(null);

  const onClickView = React.useCallback((fileName, presignedUrl) => {
    selectedContentRef.current = {
      fileName: fileName,
      signedUrl: presignedUrl
    };
    setIsViewContentModalOpen(true);
  }, []);

  const actionButtons = React.useCallback((fileName, presignedUrl) => (
    <ButtonGroup label="Default button group">
      <Button onClick={() => onClickView(fileName, presignedUrl)} appearance="primary">View</Button>
      <LinkButton href={presignedUrl} appearance="subtle-link">Download</LinkButton>
    </ButtonGroup>
  ), [onClickView]);

  React.useEffect(() => {
    const rows = files.map((file, i) => {
      return {
        key: `row-${i}-${file.key}`,
        cells: [
          {
            key: file.key,
            content: file.fileName,
          },
          {
            key: `uploader_${i}`,
            content: file.uploader,
          },
          {
            key: file.lastModified,
            content: formatTime(file.lastModified),
          },
          {
            key: i,
            content: actionButtons(file.fileName, file.presignedUrl),
          },
        ],
      }
    });
    setRows(rows);
  }, [files]);

  return (
    <>
      <DynamicTable
        caption="List of evidence"
        head={head}
        rows={rows}
      />
      <ViewContentModal
        isOpen={isViewContentModalOpen}
        closeModal={() => setIsViewContentModalOpen(false)}
        issueId={issueId}
        fileName={selectedContentRef.current?.fileName}
        signedUrl={selectedContentRef.current?.signedUrl}
      />
    </>
  );
};

export default EvidenceTable;