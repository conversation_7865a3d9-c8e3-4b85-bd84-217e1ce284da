import React, { useState, useEffect } from 'react';
import { Label, Textfield, DatePicker } from '@forge/react';
import { invoke } from '@forge/bridge';

export const DeviceEventFields = ({ context }) => {
  const [idType, setIdType] = useState('');
  const [idValue, setIdValue] = useState('');
  const [deviceEventName, setDeviceEventName] = useState('');
  const [deviceDateTime, setDeviceDateTime] = useState(new Date().toISOString());

  const onChangeIdType = (e) => {
    setIdType(e.target.value);
  };

  const onBlurIdType = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setDeviceIdType', { cloudId: context.cloudId, issueId: key, idType: e.target.value });
  };

  const onChangeIdValue = (e) => {
    setIdValue(e.target.value);
  };

  const onBlurIdValue = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setDeviceIdValue', { cloudId: context.cloudId, issueId: key, idValue: e.target.value });
  };

  const onChangeDeviceEventName = (e) => {
    setDeviceEventName(e.target.value);
  };

  const onBlurDeviceEventName = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setDeviceEventName', { cloudId: context.cloudId, issueId: key, deviceEventName: e.target.value });
  };

  const onChangeDeviceDateTime = (e) => {
    setDeviceDateTime(e.target.value);
  };

  const onBlurDeviceDateTime = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setDeviceDateTime', { cloudId: context.cloudId, issueId: key, deviceDateTime: e.target.value });
  };

  useEffect(() => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;

    const { key } = context.extension.issue;

    const fetchIdentityAndDeviceData = async () => {
      try {
        const [
          idType,
          idValue,
          deviceEventName,
          deviceDateTime,
        ] = await Promise.all([
          invoke('getDeviceIdType', { issueId: key }),
          invoke('getDeviceIdValue', { issueId: key }),
          invoke('getDeviceEventName', { issueId: key }),
          invoke('getDeviceDateTime', { issueId: key }),
        ]);

        setIdType(idType);
        setIdValue(idValue);
        setDeviceEventName(deviceEventName);
        setDeviceDateTime(deviceDateTime);
      } catch (error) {
        console.error('Failed to fetch identity/device data:', error);
      }
    };

    fetchIdentityAndDeviceData();
  }, [context]);

  return (
    <>
      <Label labelFor="idType">Device ID Type</Label>
      <Textfield id="idType" value={idType} onChange={onChangeIdType} onBlur={onBlurIdType} />
      <Label labelFor="idValue">Device ID Value</Label>
      <Textfield id="idValue" value={idValue} onChange={onChangeIdValue} onBlur={onBlurIdValue} />
      <Label labelFor="deviceEventName">Device Event Name</Label>
      <Textfield id="deviceEventName" value={deviceEventName} onChange={onChangeDeviceEventName} onBlur={onBlurDeviceEventName} />
      <Label labelFor="deviceDateTime">Device Event Time</Label>
      <Textfield type='datetime-local' id="deviceDateTime" value={deviceDateTime} onChange={onChangeDeviceDateTime} onBlur={onBlurDeviceDateTime} />
    </>
  );
};
