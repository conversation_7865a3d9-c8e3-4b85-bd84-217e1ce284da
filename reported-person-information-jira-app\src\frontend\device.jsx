import React, { useState, useEffect } from 'react';
import { Label, Textfield, DatePicker } from '@forge/react';
import { invoke } from '@forge/bridge';

export const DeviceEventFields = ({ context }) => {
  const [idType, setIdType] = useState('');
  const [idValue, setIdValue] = useState('');
  const [deviceEventName, setDeviceEventName] = useState('');
  const [deviceDateTime, setDeviceDateTime] = useState(new Date().toISOString());

  const onChangeIdType = (e) => {
    setIdType(e.target.value);
  };

  const onBlurIdType = (e) => {
    invoke('setDeviceIdType', { context, idType: e.target.value });
  };

  const onChangeIdValue = (e) => {
    setIdValue(e.target.value);
  };

  const onBlurIdValue = (e) => {
    invoke('setDeviceIdValue', { context, idValue: e.target.value });
  };

  const onChangeDeviceEventName = (e) => {
    setDeviceEventName(e.target.value);
  };

  const onBlurDeviceEventName = (e) => {
    invoke('setDeviceEventName', { context, deviceEventName: e.target.value });
  };

  const onChangeDeviceDateTime = (e) => {
    setDeviceDateTime(e.target.value);
  };

  const onBlurDeviceDateTime = (e) => {
    invoke('setDeviceDateTime', { context, deviceDateTime: e.target.value });
  };

  useEffect(() => {
    // Data will be populated via populateFieldsFromDDB function from parent component
    // Individual getter functions have been removed in favor of a single getReport function
  }, [context]);

  return (
    <>
      <Label labelFor="idType">Device ID Type</Label>
      <Textfield id="idType" value={idType} onChange={onChangeIdType} onBlur={onBlurIdType} />
      <Label labelFor="idValue">Device ID Value</Label>
      <Textfield id="idValue" value={idValue} onChange={onChangeIdValue} onBlur={onBlurIdValue} />
      <Label labelFor="deviceEventName">Device Event Name</Label>
      <Textfield id="deviceEventName" value={deviceEventName} onChange={onChangeDeviceEventName} onBlur={onBlurDeviceEventName} />
      <Label labelFor="deviceDateTime">Device Event Time</Label>
      <Textfield type='datetime-local' id="deviceDateTime" value={deviceDateTime} onChange={onChangeDeviceDateTime} onBlur={onBlurDeviceDateTime} />
    </>
  );
};
