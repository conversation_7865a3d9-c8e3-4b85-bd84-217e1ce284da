terraform {
  backend "s3" {
    bucket = "t2-d2c-terraform"
    region = "us-west-2"
    key    = "take-two-t2gp/t2gp-social-report-child-abuse-jira-app/main.tfstate"
  }
  required_providers {
    aws = {
      version = "~> 5.50"
    }
  }
}
provider "aws" {
  region = "us-east-1"
  alias  = "east1"
}

provider "aws" {
  region = "us-west-2"
  alias  = "west2"
}

data "aws_caller_identity" "current" {}