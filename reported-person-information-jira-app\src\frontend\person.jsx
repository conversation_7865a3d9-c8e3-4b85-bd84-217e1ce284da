import React, { useState, useEffect } from 'react';
import { Textfield, Label, DatePicker } from '@forge/react';
import { invoke } from '@forge/bridge';
import { AddressField } from './address';

export const PersonFields = ({ context }) => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [age, setAge] = useState(0);
  const [dateOfBirth, setDateOfBirth] = useState('');

  const onChangeReportedPersonFirstName = (e) => {
    setFirstName(e.target.value);
  }

  const onBlurReportedPersonFirstName = (e) => {
    invoke('setFirstName', { context, firstName: e.target.value });
  }

  const onChangeReportedPersonLastName = (e) => {
    setLastName(e.target.value);
  }

  const onBlurReportedPersonLastName = (e) => {
    invoke('setLastName', { context, lastName: e.target.value });
  }

  const onChangeReportedPersonPhone = (e) => {
    setPhone(e.target.value);
  }

  const onBlurReportedPersonPhone = (e) => {
    invoke('setPhone', { context, phone: e.target.value });
  }

  const onChangeReportedPersonEmail = (e) => {
    setEmail(e.target.value);
  }

  const onBlurReportedPersonEmail = (e) => {
    invoke('setEmail', { context, email: e.target.value });
  }

  const onChangeReportedPersonAge = (e) => {
    setAge(e.target.value);
  }

  const onBlurReportedPersonAge = (e) => {
    invoke('setAge', { context, age: e.target.value });
  }

  const onChangeReportedPersonDob = (e) => {
    setDateOfBirth(e);
    invoke('setDateOfBirth', { context, dob: e });
  }

  useEffect(() => {
    // Data will be populated via populateFieldsFromDDB function from parent component
    // Individual getter functions have been removed in favor of a single getReport function
  }, [context]);

  return (
    <>
      <Label labelFor="firstName">First Name</Label>
      <Textfield id="firstName" value={firstName} onChange={onChangeReportedPersonFirstName} onBlur={onBlurReportedPersonFirstName}/>
      <Label labelFor="lastName">Last Name</Label>
      <Textfield id="lastName" value={lastName} onChange={onChangeReportedPersonLastName} onBlur={onBlurReportedPersonLastName}/>
      <Label labelFor="phoneNumber">Phone Number</Label>
      <Textfield id="phoneNumber" value={phone} onChange={onChangeReportedPersonPhone} onBlur={onBlurReportedPersonPhone} />
      <Label labelFor="email">Email</Label>
      <Textfield id="email" value={email} onChange={onChangeReportedPersonEmail} onBlur={onBlurReportedPersonEmail}/>
      <Label labelFor="age">Age</Label>
      <Textfield id="age" type='number' value={age} onChange={onChangeReportedPersonAge} onBlur={onBlurReportedPersonAge} min={0} max={150} />
      <Label labelFor="dob">Date of Birth</Label>
      <DatePicker shouldShowCalendarButton id="dob" value={dateOfBirth} onChange={onChangeReportedPersonDob} />
      <AddressField context={context} />
    </>
  );
};
