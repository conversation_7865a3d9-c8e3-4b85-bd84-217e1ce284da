import React, { useState, useEffect } from 'react';
import { Textfield, Label, DatePicker } from '@forge/react';
import { invoke } from '@forge/bridge';
import { AddressField } from './address';

export const PersonFields = ({ context }) => {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [age, setAge] = useState(0);
  const [dateOfBirth, setDateOfBirth] = useState('');

  const onChangeReportedPersonFirstName = (e) => {
    setFirstName(e.target.value);
  }

  const onBlurReportedPersonFirstName = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setFirstName', { cloudId: context.cloudId, issueId: key, firstName: e.target.value });
  }

  const onChangeReportedPersonLastName = (e) => {
    setLastName(e.target.value);
  }

  const onBlurReportedPersonLastName = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setLastName', { cloudId: context.cloudId, issueId: key, lastName: e.target.value });
  }

  const onChangeReportedPersonPhone = (e) => {
    setPhone(e.target.value);
  }

  const onBlurReportedPersonPhone = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setPhone', { cloudId: context.cloudId, issueId: key, phone: e.target.value });
  }

  const onChangeReportedPersonEmail = (e) => {
    setEmail(e.target.value);
  }

  const onBlurReportedPersonEmail = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setEmail', { cloudId: context.cloudId, issueId: key, email: e.target.value });
  }

  const onChangeReportedPersonAge = (e) => {
    setAge(e.target.value);
  }

  const onBlurReportedPersonAge = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setAge', { cloudId: context.cloudId, issueId: key, age: e.target.value });
  }

  const onChangeReportedPersonDob = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    setDateOfBirth(e);
    invoke('setDateOfBirth', { cloudId: context.cloudId, issueId: key, dob: e });
  }

  useEffect(() => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;

    const fetchData = async () => {
      try {
        const { key } = context.extension.issue;

        const [
          firstName,
          lastName,
          phone,
          email,
          age,
          dob
        ] = await Promise.all([
          invoke('getFirstName', { issueId: key }),
          invoke('getLastName', { issueId: key }),
          invoke('getPhone', { issueId: key }),
          invoke('getEmail', { issueId: key }),
          invoke('getAge', { issueId: key }),
          invoke('getDateOfBirth', { issueId: key }),
        ]);

        setFirstName(firstName || '');
        setLastName(lastName || '');
        setPhone(phone || '');
        setEmail(email || '');
        setAge(age || '0');
        setDateOfBirth(dob);
      } catch (error) {
        console.error("Failed to fetch reported person's data: ", error);
      }
    };

    fetchData();
  }, [context]);

  return (
    <>
      <Label labelFor="firstName">First Name</Label>
      <Textfield id="firstName" value={firstName} onChange={onChangeReportedPersonFirstName} onBlur={onBlurReportedPersonFirstName}/>
      <Label labelFor="lastName">Last Name</Label>
      <Textfield id="lastName" value={lastName} onChange={onChangeReportedPersonLastName} onBlur={onBlurReportedPersonLastName}/>
      <Label labelFor="phoneNumber">Phone Number</Label>
      <Textfield id="phoneNumber" value={phone} onChange={onChangeReportedPersonPhone} onBlur={onBlurReportedPersonPhone} />
      <Label labelFor="email">Email</Label>
      <Textfield id="email" value={email} onChange={onChangeReportedPersonEmail} onBlur={onBlurReportedPersonEmail}/>
      <Label labelFor="age">Age</Label>
      <Textfield id="age" type='number' value={age} onChange={onChangeReportedPersonAge} onBlur={onBlurReportedPersonAge} min={0} max={150} />
      <Label labelFor="dob">Date of Birth</Label>
      <DatePicker shouldShowCalendarButton id="dob" value={dateOfBirth} onChange={onChangeReportedPersonDob} />
      <AddressField context={context} />
    </>
  );
};
