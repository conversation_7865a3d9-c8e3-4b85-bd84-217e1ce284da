import React, { useState, useEffect } from 'react';
import { Textfield, Inline, Icon, Tooltip, Label, Select, Checkbox, ErrorMessage } from '@forge/react';
import { invoke } from '@forge/bridge';
import { COUNTRY_CODES } from './address'

export const LocationInfo = ({ context }) => {
  const [city, setCity] = useState('');
  const [region, setRegion] = useState('');
  const [countryCode, setCountryCode] = useState('');
  const [verified, setVerified] = useState(false);
  const [dateTime, setDateTime] = useState('');
  const [error, setError] = useState(undefined);

  const onChangeCity = (e) => {
    setCity(e.target.value);
  };

  const onBlurCity = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setEstimatedLocationCity', { cloudId: context.cloudId, issueId: key, city: e.target.value });
  };

  const onChangeRegion = (e) => {
    setRegion(e.target.value);
  };

  const onBlurRegion = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setEstimatedLocationRegion', { cloudId: context.cloudId, issueId: key, region: e.target.value });
  };

  const onChangeCountryCode = (value) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    setCountryCode(value);
    invoke('setEstimatedLocationCountryCode', { cloudId: context.cloudId, issueId: key, countryCode: value });
  };

  const onChangeVerified = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    setVerified(e.target.checked);
    invoke('setEstimatedLocationVerified', { cloudId: context.cloudId, issueId: key, verified: e.target.checked });
  };

  const onChangeDate = (e) => {
    const now = new Date();
    if (new Date(e.target.value) > now) {
      setError("The date must be in past");
    } else {
      if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
      const { key } = context.extension.issue;
      setDateTime(e.target.value);
      invoke('setEstimatedLocationTimestamp', { cloudId: context.cloudId, issueId: key, timestamp: e.target.value });
      setError(undefined);
    }
  };

  useEffect(() => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;

    const { key } = context.extension.issue;

    const fetchIdentityAndDeviceData = async () => {
      try {
        const [
          city,
          region,
          country,
          verified,
          dateTime
        ] = await Promise.all([
          invoke('getEstimatedLocationCity', { issueId: key }),
          invoke('getEstimatedLocationRegion', { issueId: key }),
          invoke('getEstimatedLocationCountryCode', { issueId: key }),
          invoke('getEstimatedLocationVerified', { issueId: key }),
          invoke('getEstimatedLocationTimestamp', { issueId: key }),
        ]);

        setCity(city);
        setRegion(region);
        setVerified(verified);
        setCountryCode(JSON.parse(country));
        setDateTime(dateTime);
      } catch (error) {
        console.error('Failed to fetch identity/device data:', error);
      }
    };

    fetchIdentityAndDeviceData();
  }, [context]);

  return (
    <>
      <Label labelFor="city">Estimated Location City</Label>
      <Textfield id="city" value={city} onChange={onChangeCity} onBlur={onBlurCity} maxLength={255} />

      <Inline alignInline="start" space='space.050'>
        <Label labelFor="region">Reporter's Estimated Region</Label>
        <Tooltip content="The region of the estimated location.\n It must be a valid US state or territory postal abbreviation if supplied and <countryCode> is 'US'. \n must be not be blank if <city> is supplied and <countryCode> is 'US'">
          <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
        </Tooltip>
      </Inline>
      <Textfield id="region" value={region} onBlur={onBlurRegion} onChange={onChangeRegion} />

      <Label labelFor="countryCode">Reporter's Estimated Country</Label>
      <Select id="countryCode" options={COUNTRY_CODES} value={countryCode} onChange={onChangeCountryCode} />

      <Inline alignInline="start" space='space.050'>
        <Label labelFor="verified">Reporter's Location Verified</Label>
        <Tooltip content="Whether the reporter has verified the estimated location.">
          <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
        </Tooltip>
      </Inline>
      <Checkbox value="checkbox" isChecked={verified} onChange={onChangeVerified} />

      <Inline alignInline="start" space='space.050'>
        <Label labelFor="date">Reporter's Estimated Location Time</Label>
        <Tooltip content="The date the reporter made the location estimation.">
          <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
        </Tooltip>
      </Inline>
      <Textfield type='datetime-local' id="date" value={dateTime} onChange={onChangeDate} />
      {error && <ErrorMessage>{error}</ErrorMessage>}
    </>
  );
};
