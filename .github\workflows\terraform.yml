name: Deploy Terraform

on:
  push:
    paths:
      - 'terraform/**'
      - 'api-service/**'
      - 'mediaconvert/**'
  workflow_dispatch:

permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write


jobs:
  buildrun:
    runs-on: [t2gp-arc-linux]
    environment: develop
    strategy:
      fail-fast: false
      matrix:
        include:
          #- account: main
          - account: dev

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Environment Variables
        run: |
          echo TERRAFORM_VERSION=$(cat .terraform-version) >> $GITHUB_ENV
          echo AWS_ACCOUNT_ID=$(echo '${{vars.AWS_ACCOUNTS}}' | jq -c '.${{matrix.account}}.account_id' -r) >> $GITHUB_ENV
          echo AWS_REGION=$(echo '${{vars.AWS_ACCOUNTS}}' | jq -c '.${{matrix.account}}.region' -r) >> $GITHUB_ENV
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{env.AWS_ACCOUNT_ID}}:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: ${{env.AWS_REGION}}
      
      - name: Set up Node.js 
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          registry-url: https://npm.pkg.github.com
          scope: take-two-t2gp
  
      - name: Install node dependencies
        run: |
          npm ci
        working-directory: api-service/

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.1.5
          terraform_wrapper: false

      - name: Terraform Init
        run: terraform init -reconfigure -backend-config="tfbackends/${{matrix.account}}.s3.tfbackend"
        working-directory: terraform

      - name: Terraform Plan
        if: ${{matrix.account == 'main'}}
        run: terraform plan -no-color -input=false -out=tfplan
        working-directory: terraform

      - name: Terraform Plan Dev
        if: ${{matrix.account == 'dev'}}
        run: terraform plan -no-color -input=false -var-file="dev-account-overrides.tfvars" -out=tfplan
        working-directory: terraform

      - name: Sleep
        run: sleep 120
            
      - name: Terraform Apply
        run: terraform apply -input=false -auto-approve tfplan
        working-directory: terraform