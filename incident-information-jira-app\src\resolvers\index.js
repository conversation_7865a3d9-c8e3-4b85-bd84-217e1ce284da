import Resolver from '@forge/resolver';
import { kvs } from '@forge/kvs';

const resolver = new Resolver();

async function getHelper(key) {
  const storedValue = await kvs.get(key);
  return storedValue || '';
}

async function setInDDB(key, jiraCloudId, issueId, value) {
  const body = {
    fieldName: key,
    fieldValue: value
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/appData/${jiraCloudId}/${issueId}`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setInDDB. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setInDDB. Error: ${error}`);
  }
}

async function setStringHelper(key, jiraCloudId, issueId, value) {
  await kvs.set(key, value);
  await setInDDB(key, jiraCloudId, issueId, value);
}

async function setObjectHelper(key, jiraCloudId, issueId, object, stringToPresist) {
  await kvs.set(key, JSON.stringify(object));
  await setInDDB(key, jiraCloudId, issueId, stringToPresist);
}

resolver.define('setPlatform', ({ payload }) => {
  const key = `${payload.issueId}-incident-information-platform`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.platform).catch(err => console.error("Failed at setPlatform ", err));
});

resolver.define('getPlatform', ({ payload }) => {
  return getHelper(`${payload.issueId}-incident-information-platform`);
});

resolver.define('setIncidentType', ({ payload }) => {
  const key = `${payload.issueId}-incident-information-incident-type`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.type, payload.type.value).catch(err => console.error("Failed at setIncidentType ", err));
});

resolver.define('getIncidentType', ({ payload }) => {
  return getHelper(`${payload.issueId}-incident-information-incident-type`);
});

resolver.define('setEscalateToHighPriority', ({ payload }) => {
  const key = `${payload.issueId}-incident-information-escalate-to-high-priority`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.priority).catch(err => console.error("Failed at setEscalateToHighPriority ", err));
});

resolver.define('getEscalateToHighPriority', ({ payload }) => {
  return getHelper(`${payload.issueId}-incident-information-escalate-to-high-priority`);
});

resolver.define('setIncidentDate', ({ payload }) => {
  const key = `${payload.issueId}-incident-information-incident-date`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.date).catch(err => console.error("Failed at setIncidentDate ", err));
});

resolver.define('getIncidentDate', ({ payload }) => {
  return getHelper(`${payload.issueId}-incident-information-incident-date`);
});

resolver.define('setIncidentDateDescription', ({ payload }) => {
  const key = `${payload.issueId}-incident-information-incident-date-description`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.description).catch(err => console.error("Failed at setIncidentDateDescription ", err));
});

resolver.define('getIncidentDateDescription', ({ payload }) => {
  return getHelper(`${payload.issueId}-incident-information-incident-date-description`);
});

resolver.define('setReportAnnotation', ({ payload }) => {
  const key = `${payload.issueId}-incident-information-report-annotation`;
  const stringToPresist = JSON.stringify(payload.annotations.map(annotation => annotation.value));
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.annotations, stringToPresist).catch(err => console.error("Failed at setReportAnnotation ", err));
});

resolver.define('getReportAnnotation', ({ payload }) => {
  return getHelper(`${payload.issueId}-incident-information-report-annotation`);
});

export const handler = resolver.getDefinitions();
