variable "business_svc" { default = "shared" }
variable "env" { default = "prod" }
variable "svc_criticality" { default = "internal_critical" }
variable "description" { default = "t2gp-social-report-evidence-viewer " }
variable "s3_bucket" { default = "t2gp-social-develop" }
variable "aws_region" { default = "us-east-1" }
variable "prefix" { default = "t2gp-social" }

locals {
  provider_default_tags = {
    "global:business_svc"    = var.business_svc
    "global:iac_platform"    = "terraform"
    "global:env"             = var.env
    "global:svc_criticality" = var.svc_criticality
    "global:iac_src"         = "https://github.com/take-two-t2gp/t2gp-social-report-child-abuse-jira-app"
    description              = var.description
    aws_account              = data.aws_caller_identity.current.account_id
  }
}