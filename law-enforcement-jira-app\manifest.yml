modules:
  jira:issueContext:
    - key: law-enforcement-jira-app-issue-context
      resource: main
      resolver:
        function: resolver
      render: native
      title: Law Enforcement Information
      description: Law Enforcement Information
      label: Law Enforcement Information
  function:
    - key: resolver
      handler: index.handler
resources:
  - key: main
    path: src/frontend/index.jsx
app:
  runtime:
    name: nodejs22.x
    memoryMB: 256
    architecture: arm64
  id: ari:cloud:ecosystem::app/b652d521-1746-4688-b48d-39492b915588
permissions:
  scopes:
    - storage:app
  external:
    fetch:
      backend:
        - 'https://hg6e00eso6.execute-api.us-west-2.amazonaws.com/*'