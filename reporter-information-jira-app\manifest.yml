modules:
  jira:issueContext:
    - key: reporter-information-issue-context
      resource: main
      resolver:
        function: resolver
      render: native
      title: Reporter Information
      description: Report information JIRA app.
      label: Reporter Information
  function:
    - key: resolver
      handler: index.handler
resources:
  - key: main
    path: src/frontend/index.jsx
app:
  runtime:
    name: nodejs22.x
    memoryMB: 256
    architecture: arm64
  id: ari:cloud:ecosystem::app/c9ebcf83-119d-4e8b-9bef-a1efa71af098
permissions:
  scopes:
    - storage:app
    - read:jira-user
  external:
    fetch:
      backend:
        - 'https://t2gp-social-legal-api.d2dragon.net/*'