import Resolver from '@forge/resolver';
import { kvs } from '@forge/kvs';

const resolver = new Resolver();

async function getHelper(key) {
  const storedValue = await kvs.get(key);
  return storedValue || '';
}

async function setHelper(key, context, value) {
  await kvs.set(key, value);

  const body = {
    fieldName: key,
    fieldValue: value,
    user: context.user
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/ticket/${context.issueId}/customFieldUpdate/auditLogs`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setHelper. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setHelper. Error: ${error}`);
  }
}

resolver.define('setInternetIncidentType', ({ payload }) => {
  const key = `${payload.context.issueId}-internet-details-incident-type`;
  return setHelper(key, payload.context, JSON.stringify(payload.type)).catch(err => console.error("Failed at setInternetIncidentType", err));
});

resolver.define('getInternetIncidentType', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-incident-type`);
});

resolver.define('setUrl', ({ payload }) => {
  const key = `${payload.context.issueId}-internet-details-url`;
  return setHelper(key, payload.context, payload.url).catch(err => console.error("Failed at setUrl", err));
});

resolver.define('getUrl', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-url`);
});

resolver.define('setThirdParty', ({ payload }) => {
  const key = `${payload.context.issueId}-internet-details-third-party`;
  return setHelper(key, payload.context, payload.thirdParty).catch(err => console.error("Failed at setThirdParty", err));
});

resolver.define('getThirdParty', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-third-party`);
});

resolver.define('setChatClient', ({ payload }) => {
  const key = `${payload.context.issueId}-internet-details-chat-client`;
  return setHelper(key, payload.context, payload.chatClient).catch(err => console.error("Failed at setChatClient", err));
});

resolver.define('getChatClient', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-chat-client`);
});

resolver.define('setChatRoomName', ({ payload }) => {
  const key = `${payload.context.issueId}-internet-details-chat-room-name`;
  return setHelper(key, payload.context, payload.chatRoomName).catch(err => console.error("Failed at setChatRoomName", err));
});

resolver.define('getChatRoomName', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-chat-room-name`);
});

resolver.define('setContent', ({ payload }) => {
  const key = `${payload.context.issueId}-internet-details-content`;
  return setHelper(key, payload.context, payload.content).catch(err => console.error("Failed at setContent", err));
});

resolver.define('getContent', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-content`);
});

resolver.define('setPageAdditionalInfo', ({ payload }) => {
  const key = `${payload.context.issueId}-internet-details-page-additional-info`;
  return setHelper(key, payload.context, payload.pageAdditionalInfo).catch(err => console.error("Failed at setPageAdditionalInfo", err));
});

resolver.define('getPageAdditionalInfo', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-page-additional-info`);
});

resolver.define('setChatAdditionalInfo', ({ payload }) => {
  const key = `${payload.context.issueId}-internet-details-chat-additional-info`;
  return setHelper(key, payload.context, payload.chatAdditionalInfo).catch(err => console.error("Failed at setChatAdditionalInfo", err));
});

resolver.define('getChatAdditionalInfo', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-chat-additional-info`);
});

resolver.define('setConsole', ({ payload }) => {
  const key = `${payload.context.issueId}-online-game-console`;
  return setHelper(key, payload.context, payload.console).catch(err => console.error("Failed at setConsole", err));
});

resolver.define('getConsole', ({ payload }) => {
  return getHelper(`${payload.issueId}-online-game-console`);
});

resolver.define('setGameName', ({ payload }) => {
  const key = `${payload.context.issueId}-online-game-game-name`;
  return setHelper(key, payload.context, payload.gameName).catch(err => console.error("Failed at setGameName", err));
});

resolver.define('getGameName', ({ payload }) => {
  return getHelper(`${payload.issueId}-online-game-game-name`);
});

resolver.define('setOnlineGameContent', ({ payload }) => {
  const key = `${payload.context.issueId}-online-game-content`;
  return setHelper(key, payload.context, payload.content).catch(err => console.error("Failed at setOnlineGameContent", err));
});

resolver.define('getOnlineGameContent', ({ payload }) => {
  return getHelper(`${payload.issueId}-online-game-content`);
});

resolver.define('setOnlineGameAdditionalInfo', ({ payload }) => {
  const key = `${payload.context.issueId}-online-game-additional-info`;
  return setHelper(key, payload.context, payload.additionalInfo).catch(err => console.error("Failed at setOnlineGameAdditionalInfo", err));
});

resolver.define('getOnlineGameAdditionalInfo', ({ payload }) => {
  return getHelper(`${payload.issueId}-online-game-additional-info`);
});

export const handler = resolver.getDefinitions();
