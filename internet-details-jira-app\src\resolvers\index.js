import Resolver from '@forge/resolver';
import { kvs } from '@forge/kvs';

async function getHelper(key) {
  const storedValue = await kvs.get(key);
  return storedValue || '';
}

async function setInDDB(key, jiraCloudId, issueId, value) {
  const body = {
    fieldName: key,
    fieldValue: value
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/appData/${jiraCloudId}/${issueId}`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setInDDB. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setInDDB. Error: ${error}`);
  }
}


async function setStringHelper(key, jiraCloudId, issueId, value) {
  await kvs.set(key, value);
  await setInDDB(key, jiraCloudId, issueId, value);
}

async function setObjectHelper(key, jiraCloudId, issueId, object, stringToPresist) {
  await kvs.set(key, JSON.stringify(object));
  await setInDDB(key, jiraCloudId, issueId, stringToPresist);
}

const resolver = new Resolver();

resolver.define('setInternetIncidentType', ({ payload }) => {
  const key = `${payload.issueId}-internet-details-incident-type`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.type, payload.type.value).catch(err => console.error("Failed at setInternetIncidentType ", err));
});

resolver.define('getInternetIncidentType', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-incident-type`);
});

// Setters and Getters for 'url'
resolver.define('setUrl', ({ payload }) => {
  const key = `${payload.issueId}-internet-details-url`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.url).catch(err => console.error("Failed at setUrl ", err));
});

resolver.define('getUrl', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-url`);
});

// Setters and Getters for 'thirdParty'
resolver.define('setThirdParty', ({ payload }) => {
  const key = `${payload.issueId}-internet-details-third-party`; // Changed to 'third-party'
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.thirdParty).catch(err => console.error("Failed at setThirdParty ", err));
});

resolver.define('getThirdParty', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-third-party`); // Changed to 'third-party'
});

// Setters and Getters for 'chatClient'
resolver.define('setChatClient', ({ payload }) => {
  const key = `${payload.issueId}-internet-details-chat-client`; // Changed to 'chat-client'
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.chatClient).catch(err => console.error("Failed at setChatClient ", err));
});

resolver.define('getChatClient', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-chat-client`); // Changed to 'chat-client'
});

// Setters and Getters for 'chatRoomName'
resolver.define('setChatRoomName', ({ payload }) => {
  const key = `${payload.issueId}-internet-details-chat-room-name`; // Changed to 'chat-room-name'
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.chatRoomName).catch(err => console.error("Failed at setChatRoomName ", err));
});

resolver.define('getChatRoomName', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-chat-room-name`); // Changed to 'chat-room-name'
});

// Setters and Getters for 'content'
resolver.define('setContent', ({ payload }) => {
  const key = `${payload.issueId}-internet-details-content`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.content).catch(err => console.error("Failed at setContent ", err));
});

resolver.define('getContent', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-content`);
});

// Setters and Getters for 'pageAdditionalInfo'
resolver.define('setPageAdditionalInfo', ({ payload }) => {
  const key = `${payload.issueId}-internet-details-page-additional-info`; // Changed to 'page-additional-info'
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.pageAdditionalInfo).catch(err => console.error("Failed at setPageAdditionalInfo ", err));
});

resolver.define('getPageAdditionalInfo', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-page-additional-info`); // Changed to 'page-additional-info'
});

// Setters and Getters for 'chatAdditionalInfo'
resolver.define('setChatAdditionalInfo', ({ payload }) => {
  const key = `${payload.issueId}-internet-details-chat-additional-info`; // Changed to 'chat-additional-info'
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.chatAdditionalInfo).catch(err => console.error("Failed at setChatAdditionalInfo ", err));
});

resolver.define('getChatAdditionalInfo', ({ payload }) => {
  return getHelper(`${payload.issueId}-internet-details-chat-additional-info`); // Changed to 'chat-additional-info'
});

resolver.define('setConsole', ({ payload }) => {
  const key = `${payload.issueId}-online-game-console`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.console).catch(err => console.error("Failed at setConsole ", err));
});

resolver.define('getConsole', ({ payload }) => {
  return getHelper(`${payload.issueId}-online-game-console`);
});

resolver.define('setGameName', ({ payload }) => {
  const key = `${payload.issueId}-online-game-game-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.gameName).catch(err => console.error("Failed at setGameName ", err));
});

resolver.define('getGameName', ({ payload }) => {
  return getHelper(`${payload.issueId}-online-game-game-name`);
});

resolver.define('setOnlineGameContent', ({ payload }) => {
  const key = `${payload.issueId}-online-game-content`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.content).catch(err => console.error("Failed at setContent ", err));
});

resolver.define('getOnlineGameContent', ({ payload }) => {
  return getHelper(`${payload.issueId}-online-game-content`);
});

resolver.define('setOnlineGameAdditionalInfo', ({ payload }) => {
  const key = `${payload.issueId}-online-game-additional-info`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.additionalInfo).catch(err => console.error("Failed at setAdditionalInfo ", err));
});

resolver.define('getOnlineGameAdditionalInfo', ({ payload }) => {
  return getHelper(`${payload.issueId}-online-game-additional-info`);
});

export const handler = resolver.getDefinitions();
