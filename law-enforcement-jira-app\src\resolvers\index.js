import Resolver from '@forge/resolver';
import { kvs } from '@forge/kvs';

const resolver = new Resolver();

async function getHelper(key) {
  const storedValue = await kvs.get(key);
  return storedValue || '';
}

async function setInDDB(key, jiraCloudId, issueId, value) {
  const body = {
    fieldName: key,
    fieldValue: value
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/appData/${jiraCloudId}/${issueId}`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setInDDB. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setInDDB. Error: ${error}`);
  }
}

async function setStringHelper(key, jiraCloudId, issueId, value) {
  await kvs.set(key, value);
  await setInDDB(key, jiraCloudId, issueId, value);
}

async function setObjectHelper(key, jiraCloudId, issueId, object, stringToPresist) {
  await kvs.set(key, JSON.stringify(object));
  await setInDDB(key, jiraCloudId, issueId, stringToPresist);
}

// Setters and Getters for 'agencyName'
resolver.define('setAgencyName', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-agency-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.agencyName).catch(err => console.error("Failed at setAgencyName", err));
});

resolver.define('getAgencyName', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-agency-name`);
});

// Setters and Getters for 'caseNumber'
resolver.define('setCaseNumber', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-case-number`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.caseNumber).catch(err => console.error("Failed at setCaseNumber", err));
});

resolver.define('getCaseNumber', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-case-number`);
});

// Setters and Getters for 'reportedToLE'
resolver.define('setReportedToLE', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-reported-to-le`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.reportedToLE).catch(err => console.error("Failed at setReportedToLE", err));
});

resolver.define('getReportedToLE', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-reported-to-le`);
});

// Setters and Getters for 'servedLegalDomestic'
resolver.define('setServedLegalDomestic', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-served-legal-domestic`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.servedLegalDomestic).catch(err => console.error("Failed at setServedLegalDomestic", err));
});

resolver.define('getServedLegalDomestic', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-served-legal-domestic`);
});

// Setters and Getters for 'servedLegalIntl'
resolver.define('setServedLegalIntl', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-served-legal-intl`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.servedLegalIntl).catch(err => console.error("Failed at setServedLegalIntl", err));
});

resolver.define('getServedLegalIntl', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-served-legal-intl`);
});

// Setters and Getters for 'fleaCountry'
resolver.define('setFleaCountry', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-flea-country`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.fleaCountry, payload.fleaCountry.value).catch(err => console.error("Failed at setFleaCountry", err));
});

resolver.define('getFleaCountry', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-flea-country`);
});

resolver.define('setFirstName', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-first-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.firstName).catch(err => console.error("Failed at setFirstName", err));
});

resolver.define('getFirstName', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-first-name`);
});

resolver.define('setLastName', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-last-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.lastName).catch(err => console.error("Failed at setLastName", err));
});

resolver.define('getLastName', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-last-name`);
});

resolver.define('setPhone', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-phone`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.phone).catch(err => console.error("Failed at setPhone", err));
});

resolver.define('getPhone', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-phone`);
});

resolver.define('setEmail', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-email`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.email).catch(err => console.error("Failed at setEmail", err));
});

resolver.define('getEmail', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-email`);
});

resolver.define('setStreet', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-street`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.street).catch(err => console.error("Failed at setStreet", err));
});

resolver.define('getStreet', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-street`);
});

resolver.define('setCity', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-city`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.city).catch(err => console.error("Failed at setCity", err));
});

resolver.define('getCity', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-city`);
});

resolver.define('setZipCode', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-zip-code`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.zipCode).catch(err => console.error("Failed at setZipCode", err));
});

resolver.define('getZipCode', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-zip-code`);
});

resolver.define('setState', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-state`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.state, payload.state.value).catch(err => console.error("Failed at setState", err));
});

resolver.define('getState', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-state`);
});

resolver.define('setNonUsaState', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-non-usa-state`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.nonUsaState).catch(err => console.error("Failed at setNonUsaState", err));
});

resolver.define('getNonUsaState', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-non-usa-state`);
});

resolver.define('setCountry', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-country`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.country, payload.country.value).catch(err => console.error("Failed at setCountry", err));
});

resolver.define('getCountry', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-country`);
});

resolver.define('setAddressType', ({ payload }) => {
  const key = `${payload.issueId}-law-enforcement-address-type`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.type, payload.type.value).catch(err => console.error("Failed at setType", err));
});

resolver.define('getAddressType', ({ payload }) => {
  return getHelper(`${payload.issueId}-law-enforcement-address-type`);
});

export const handler = resolver.getDefinitions();
