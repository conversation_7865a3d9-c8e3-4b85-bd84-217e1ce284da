import { But<PERSON> } from "primereact/button";
import { InputText } from "primereact/inputtext";
import React from 'react';
import { useNavigate } from "react-router-dom";

const Home = () => {
  const [issueId, setIssueId] = React.useState('');

  const navigate = useNavigate();
  const toEvidenceView = () => {
    navigate(`/evidenceList/${issueId}`, { replace: true });
  }

  return (
    <div className="flex flex-nowrap flex-column align-items-center">
      <h1 className="page-header">T2GP Evidence Viewer</h1>
      <div className="p-inputgroup flex-1" style={{maxWidth: '200px'}}>
        <InputText value={issueId} onChange={(e) => setIssueId(e.target.value)}placeholder="Enter an issue ID" />
        <Button onClick={toEvidenceView} icon="pi pi-search" className="p-button-warning" />
      </div>
    </div>
  );
};

export default Home;
