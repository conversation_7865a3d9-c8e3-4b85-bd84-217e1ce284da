{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@okta/okta-auth-js": "^7.12.1", "@okta/okta-react": "^6.10.0", "@types/node": "^22.15.17", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "crc-32": "^1.2.2", "form-data": "^4.0.2", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.5.3", "react-scripts": "5.0.1", "typescript": "^4.9.5"}, "scripts": {"start": "react-scripts start", "serve": "serve -s build", "build": "react-scripts build", "test": "react-scripts test"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"video.js": "^8.22.0"}}