import Resolver from '@forge/resolver';
import { kvs } from '@forge/kvs';

async function getHelper(key) {
  const storedValue = await kvs.get(key);
  return storedValue || '';
}

async function setInDDB(key, jiraCloudId, issueId, value) {
  const body = {
    fieldName: key,
    fieldValue: value
  };

  try {
    const response = await fetch(`${process.env.API_HOST}/ticket/${context.issueId}/customFieldUpdate/auditLogs`, {
      method: "PUT",
      headers: {
        "x-api-key": process.env.API_KEY,
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Failed to setInDDB. Status: ${response.status}. Detail: ${error}`);
    }

    return response.ok;
  } catch (error) {
    console.error(`Failed to setInDDB. Error: ${error}`);
  }
}

async function setStringHelper(key, jiraCloudId, issueId, value) {
  await kvs.set(key, value);
  await setInDDB(key, jiraCloudId, issueId, value);
}

async function setObjectHelper(key, jiraCloudId, issueId, object, stringToPresist) {
  await kvs.set(key, JSON.stringify(object));
  await setInDDB(key, jiraCloudId, issueId, stringToPresist);
}

const resolver = new Resolver();

resolver.define('setFirstName', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-first-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.firstName).catch(err => console.error("Failed at setFirstName", err));
});

resolver.define('getFirstName', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-first-name`);
});

resolver.define('setLastName', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-last-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.lastName).catch(err => console.error("Failed at setLastName", err));
});

resolver.define('getLastName', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-last-name`);
});

resolver.define('setPhone', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-phone`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.phone).catch(err => console.error("Failed at setPhone", err));
});

resolver.define('getPhone', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-phone`);
});

resolver.define('setEmail', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-email`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.email).catch(err => console.error("Failed at setEmail", err));
});

resolver.define('getEmail', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-email`);
});

resolver.define('setAge', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-age`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.age).catch(err => console.error("Failed at setAge", err));
});

resolver.define('getAge', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-age`);
});

resolver.define('setDateOfBirth', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-dob`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.dob).catch(err => console.error("Failed at setDateOfBirth", err));
});

resolver.define('getDateOfBirth', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-dob`);
});

resolver.define('setStreet', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-street`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.street).catch(err => console.error("Failed at setStreet", err));
});

resolver.define('getStreet', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-street`);
});

resolver.define('setCity', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-city`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.city).catch(err => console.error("Failed at setCity", err));
});

resolver.define('getCity', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-city`);
});

resolver.define('setZipCode', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-zip-code`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.zipCode).catch(err => console.error("Failed at setZipCode", err));
});

resolver.define('getZipCode', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-zip-code`);
});

resolver.define('setState', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-state`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.state, payload.state.value).catch(err => console.error("Failed at setState", err));
});

resolver.define('getState', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-state`);
});

resolver.define('setNonUsaState', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-non-usa-state`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.nonUsaState).catch(err => console.error("Failed at setNonUsaState", err));
});

resolver.define('getNonUsaState', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-non-usa-state`);
});

resolver.define('setCountry', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-country`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.country, payload.country.value).catch(err => console.error("Failed at setCountry", err));
});

resolver.define('getCountry', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-country`);
});

resolver.define('setAddressType', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-address-type`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.type, payload.type.value).catch(err => console.error("Failed at setType", err));
});

resolver.define('getAddressType', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-address-type`);
});

resolver.define('setSchoolName', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-school-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.schoolName).catch(err => console.error("Failed at setProfileUrl", err));
});

resolver.define('getSchoolName', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-school-name`);
});

resolver.define('setEspService', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-esp-service`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.espService).catch(err => console.error("Failed at setProfileUrl", err));
});

resolver.define('getEspService', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-esp-service`);
});

resolver.define('setScreenName', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-screen-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.screenName).catch(err => console.error("Failed at setProfileUrl", err));
});

resolver.define('getScreenName', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-screen-name`);
});

resolver.define('setDisplayName', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-display-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.displayName).catch(err => console.error("Failed at setProfileUrl", err));
});

resolver.define('getDisplayName', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-display-name`);
});

resolver.define('setProfileUrl', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-profile-url`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.profileUrl).catch(err => console.error("Failed at setProfileUrl", err));
});

resolver.define('getProfileUrl', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-profile-url`);
});

resolver.define('setProfileBio', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-profile-bio`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.profileBio).catch(err => console.error("Failed at setProfileBio", err));
});

resolver.define('getProfileBio', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-profile-bio`);
});

resolver.define('setCompromisedAccount', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-compromised-account`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.compromisedAccount).catch(err => console.error("Failed at setCompromisedAccount", err));
});

resolver.define('getCompromisedAccount', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-compromised-account`);
});

resolver.define('setGroupIdentifier', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-group-identifier`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.groupIdentifier).catch(err => console.error("Failed at setGroupIdentifier", err));
});

resolver.define('getGroupIdentifier', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-group-identifier`);
});

resolver.define('setThirdPartyUserReported', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-third-party-user-reported`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.thirdPartyUserReported).catch(err => console.error("Failed at setThirdPartyUserReported", err));
});

resolver.define('getThirdPartyUserReported', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-third-party-user-reported`);
});

resolver.define('setPriorCTReports', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-prior-ct-reports`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.priorCTReports).catch(err => console.error("Failed at setPriorCTReports", err));
});

resolver.define('getPriorCTReports', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-prior-ct-reports`);
});

resolver.define('setAdditionalInfo', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-additional-info`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.additionalInfo).catch(err => console.error("Failed at setPriorCTReports", err));
});

resolver.define('getAdditionalInfo', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-additional-info`);
});

resolver.define('setEspIdentifer', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-esp-identifier`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.espIdentifer).catch(err => console.error("Failed at setEspIdentifer", err));
});

resolver.define('getEspIdentifer', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-esp-identifier`);
});

resolver.define('setDeviceIdType', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-device-id-type`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.idType).catch(err => console.error("Failed at setIdType", err));
});

resolver.define('getDeviceIdType', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-device-id-type`);
});

resolver.define('setDeviceIdValue', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-device-id-value`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.idValue).catch(err => console.error("Failed at setIdValue", err));
});

resolver.define('getDeviceIdValue', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-device-id-value`);
});

resolver.define('setDeviceEventName', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-device-event-name`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.deviceEventName).catch(err => console.error("Failed at setDeviceEventName", err));
});

resolver.define('getDeviceEventName', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-device-event-name`);
});

resolver.define('setDeviceDateTime', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-device-date-time`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.deviceDateTime).catch(err => console.error("Failed at setDeviceDateTime", err));
});

resolver.define('getDeviceDateTime', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-device-date-time`);
});

resolver.define('setEstimatedLocationCity', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-estimated-location-city`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.city).catch(err => console.error("Failed at setCity", err));
});

resolver.define('getEstimatedLocationCity', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-estimated-location-city`);
});

resolver.define('setEstimatedLocationRegion', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-estimated-location-region`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.region).catch(err => console.error("Failed at setRegion", err));
});

resolver.define('getEstimatedLocationRegion', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-estimated-location-region`);
});

resolver.define('setEstimatedLocationCountryCode', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-estimated-location-country-code`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.countryCode, payload.countryCode.value).catch(err => console.error("Failed at setCountryCode", err));
});

resolver.define('getEstimatedLocationCountryCode', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-estimated-location-country-code`);
});

resolver.define('setEstimatedLocationVerified', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-estimated-location-verified`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.verified).catch(err => console.error("Failed at setVerified", err));
});

resolver.define('getEstimatedLocationVerified', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-estimated-location-verified`);
});

resolver.define('setEstimatedLocationTimestamp', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-estimated-location-timestamp`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.timestamp).catch(err => console.error("Failed at setTimestamp", err));
});

resolver.define('getEstimatedLocationTimestamp', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-estimated-location-timestamp`);
});

resolver.define('setIpAddress', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-ip-capture-event-ip-address`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.ipAddress).catch(err => console.error("Failed at setIpAddress", err));
});

resolver.define('setEventName', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-ip-capture-event-event-name`;
  return setObjectHelper(key, payload.cloudId, payload.issueId, payload.eventName, payload.eventName.value).catch(err => console.error("Failed at setEventName", err));
});

resolver.define('setIpEventDateTime', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-ip-capture-event-date-time`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.dateTime).catch(err => console.error("Failed at setDateTime", err));
});

resolver.define('setPossibleProxy', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-ip-capture-event-possible-proxy`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.possibleProxy).catch(err => console.error("Failed at setPossibleProxy", err));
});

resolver.define('setPort', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-ip-capture-event-port`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.port).catch(err => console.error("Failed at setPort", err));
});

resolver.define('getIpAddress', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-ip-capture-event-ip-address`);
});

resolver.define('getEventName', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-ip-capture-event-event-name`);
});

resolver.define('getIpEventDateTime', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-ip-capture-event-date-time`);
});

resolver.define('getPossibleProxy', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-ip-capture-event-possible-proxy`);
});

resolver.define('getPort', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-ip-capture-event-port`);
});

resolver.define('setIsDisabledTemporarily', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-account-is-disabled-temporarily`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.isDisabled).catch(err => console.error("Failed at setIsDisabled", err));
});

resolver.define('setDisabledDateTemporarily', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-account-disabled-date-temporarily`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.disabledDate).catch(err => console.error("Failed at setDisabledDate", err));
});

resolver.define('setUserNotifiedTemporarily', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-account-user-notified-temporarily`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.userNotified).catch(err => console.error("Failed at setUserNotified", err));
});

resolver.define('setUserNotifiedDateTemporarily', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-account-user-notified-date-temporarily`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.userNotifiedDate).catch(err => console.error("Failed at setUserNotifiedDate", err));
});

resolver.define('setReenabledDateTemporarily', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-account-reenabled-date-temporarily`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.reenabledDate).catch(err => console.error("Failed at setReenabledDate", err));
});

resolver.define('getIsDisabledTemporarily', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-account-is-disabled-temporarily`);
});

resolver.define('getDisabledDateTemporarily', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-account-disabled-date-temporarily`);
});

resolver.define('getUserNotifiedTemporarily', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-account-user-notified-temporarily`);
});

resolver.define('getUserNotifiedDateTemporarily', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-account-user-notified-date-temporarily`);
});

resolver.define('getReenabledDateTemporarily', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-account-reenabled-date-temporarily`);
});

resolver.define('setIsDisabledPermanently', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-account-is-disabled-Permanently`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.isDisabled).catch(err => console.error("Failed at setIsDisabled", err));
});

resolver.define('setDisabledDatePermanently', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-account-disabled-date-Permanently`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.disabledDate).catch(err => console.error("Failed at setDisabledDate", err));
});

resolver.define('setUserNotifiedPermanently', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-account-user-notified-Permanently`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.userNotified).catch(err => console.error("Failed at setUserNotified", err));
});

resolver.define('setUserNotifiedDatePermanently', ({ payload }) => {
  const key = `${payload.issueId}-child-victim-account-user-notified-date-Permanently`;
  return setStringHelper(key, payload.cloudId, payload.issueId, payload.userNotifiedDate).catch(err => console.error("Failed at setUserNotifiedDate", err));
});

resolver.define('getIsDisabledPermanently', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-account-is-disabled-Permanently`);
});

resolver.define('getDisabledDatePermanently', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-account-disabled-date-Permanently`);
});

resolver.define('getUserNotifiedPermanently', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-account-user-notified-Permanently`);
});

resolver.define('getUserNotifiedDatePermanently', ({ payload }) => {
  return getHelper(`${payload.issueId}-child-victim-account-user-notified-date-Permanently`);
});

export const handler = resolver.getDefinitions();
