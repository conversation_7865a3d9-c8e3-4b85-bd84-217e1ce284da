import {
  FileUpload,
  FileUploadFile,
  FileUploadHandlerEvent,
  FileUploadRemoveEvent,
  FileUploadSelectEvent,
  ItemTemplateOptions,
} from "primereact/fileupload";
import { uploadFile } from "./api";
import { FileUploadDetails } from "./dataModel";
import CRC32 from "crc-32";
import React from "react";
import { Tag } from "primereact/tag";
import { Button } from "primereact/button";
import { getFileType, isAllowedFile } from "./utility";

const chooseOptions = {
  icon: "pi pi-fw pi-images",
  className: "custom-choose-btn p-button-rounded p-button-outlined",
};
const uploadOptions = {
  icon: "pi pi-fw pi-cloud-upload",
  className:
    "custom-upload-btn p-button-success p-button-rounded p-button-outlined",
};
const cancelOptions = {
  icon: "pi pi-fw pi-times",
  className:
    "custom-cancel-btn p-button-danger p-button-rounded p-button-outlined",
};

enum FileStatue {
  Pending,
  Uploading,
  Completed,
  Fail,
  Unknown,
}

interface EvidenceFileUploadProps {
  issueId: string | undefined;
  tenant?: string;
  onUploadCompleted: (fileName: string) => void;
  onUploadError: (fileName: string) => void;
  onDisallowedFileSelect: (fileName: string) => void;
}

const EvidenceFileUpload = (props: EvidenceFileUploadProps) => {
  const [filesState, setFilesState] = React.useState<Map<string, FileStatue>>(
    new Map<string, FileStatue>()
  );

  const uploadRef = React.useRef<FileUpload>(null);

  const onSelect = React.useCallback((event: FileUploadSelectEvent) => {
    const newMap: Map<string, FileStatue> = new Map();
    const files: FileUploadFile[] = [];
    event.files.forEach(f => {
      if (!isAllowedFile(f.name)) {
        props.onDisallowedFileSelect(f.name);
      } else {
        newMap.set(f.name, FileStatue.Pending);
        files.push(f);
      }
    })

    if (uploadRef.current) {
      uploadRef.current.setFiles(files);
    }
    setFilesState(newMap);
  }, [props]);

  const onRemove = React.useCallback(
    (event: FileUploadRemoveEvent) => {
      filesState.delete(event.file.name);
      setFilesState((preState) => {
        const newMap = new Map(preState);
        newMap.delete(event.file.name);
        return newMap;
      });
    },
    [filesState]
  );

  const upload = React.useCallback(
    async (file: FileUploadFile, hex: string) => {
      const detail: FileUploadDetails = {
        originalFileName: file.name,
        originalFileHash: [
          {
            hashType: "crc-32",
            value: hex,
          },
        ],
      };

      uploadFile(props.issueId ?? "", detail, file)
        .then((response) => {
          props.onUploadCompleted(file.name);
          setFilesState((preState) => {
            const newMap = new Map(preState);
            newMap.set(file.name, FileStatue.Completed);
            return newMap;
          });
        })
        .catch((error) => {
          props.onUploadError(file.name);
          setFilesState((preState) => {
            const newMap = new Map(preState);
            newMap.set(file.name, FileStatue.Fail);
            return newMap;
          });
        });
    },
    [props]
  );

  const uploadHandler = React.useCallback(
    async (event: FileUploadHandlerEvent) => {
      event.files.forEach((file) => {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = async () => {
          const arrayBuffer = reader.result as ArrayBuffer;
          const uint8Array = new Uint8Array(arrayBuffer);
          const crc = CRC32.buf(uint8Array) >>> 0;
          const hex = crc.toString(16).padStart(8, "0");
          upload(file, hex);
        };

        setFilesState((preState) => {
          const newMap = new Map(preState);
          newMap.set(file.name, FileStatue.Uploading);
          return newMap;
        });

        reader.readAsArrayBuffer(file);
      });
    },
    [upload]
  );

  const filePreview = React.useCallback((file: FileUploadFile) => {
    const type = getFileType(file.name);
    return type === "Image" ? (
      <img
        alt={file.name}
        role="presentation"
        src={file.objectURL}
        width={100}
      />
    ) : (
      <></>
    );
  }, []);

  const fileStateTagSeverity = React.useCallback(
    (fileName: string) => {
      const state = filesState.get(fileName);
      switch (state) {
        case FileStatue.Pending:
          return "warning";
        case FileStatue.Uploading:
          return "info";
        case FileStatue.Completed:
          return "success";
        case FileStatue.Fail:
          return "danger";
        default:
          return "secondary";
      }
    },
    [filesState]
  );

  const itemTemplate = React.useCallback((inFile: object, props: ItemTemplateOptions) => {
    const file = inFile as FileUploadFile;
    return (
      <div className="flex align-items-center flex-wrap">
        <div className="flex align-items-center flex-grow-1" style={{ maxWidth: 'calc(100% - 150px)' }}>
          {filePreview(file)}
          <span className="flex flex-column text-left ml-3" style={{ flex: '1 1 auto', minWidth: 0, overflowWrap: 'break-word' }}>
            {file.name}
          </span>
        </div>
        <Tag
          value={FileStatue[filesState.get(file.name) ?? 4]}
          severity={fileStateTagSeverity(file.name)}
          className="px-3 py-2 mr-3 ml-3"
        />
        <Button
          type="button"
          icon="pi pi-times"
          className="p-button-outlined p-button-rounded p-button-danger ml-auto"
          onClick={props.onRemove}
        />
      </div>
    );
  }, [filePreview, fileStateTagSeverity, filesState]);

  return (
    <FileUpload
      ref={uploadRef}
      style={{
        minWidth: "300px",
        maxWidth: "400px",
      }}
      multiple
      customUpload={true}
      uploadHandler={uploadHandler}
      itemTemplate={itemTemplate}
      emptyTemplate={
        <p className="m-0">Drag and drop files to here to upload.</p>
      }
      onSelect={onSelect}
      onRemove={onRemove}
      chooseOptions={chooseOptions}
      uploadOptions={uploadOptions}
      cancelOptions={cancelOptions}
      pt={{
        buttonbar: {
          className: 'flex flex-column'
        }
      }}
    />
  );
};

export default EvidenceFileUpload;
