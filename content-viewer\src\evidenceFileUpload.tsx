import {
  FileUpload,
  FileUploadFile,
  FileUploadHandlerEvent,
  FileUploadRemoveEvent,
  FileUploadSelectEvent,
  ItemTemplateOptions,
} from "primereact/fileupload";
import { uploadFile } from "./api";
import { FileUploadDetails } from "./dataModel";
import CRC32 from "crc-32";
import React from "react";
import { Tag } from "primereact/tag";
import { Button } from "primereact/button";
import { formatBytes, getFileType, isAllowedFile, truncateFileName } from "./utility";
import { FileUploadPtOptions } from "./style";

const chooseOptions = {
  icon: "pi pi-fw pi-images",
  className: "custom-choose-btn p-button-rounded p-button-outlined",
};
const uploadOptions = {
  icon: "pi pi-fw pi-cloud-upload",
  className:
    "custom-upload-btn p-button-success p-button-rounded p-button-outlined",
};
const cancelOptions = {
  icon: "pi pi-fw pi-times",
  className:
    "custom-cancel-btn p-button-danger p-button-rounded p-button-outlined",
};

enum FileStatue {
  Pending,
  Uploading,
  Completed,
  Fail,
  Unknown,
}

interface EvidenceFileUploadProps {
  issueId: string | undefined;
  tenant?: string;
  onUploadCompleted: (fileName: string) => void;
  onUploadError: (fileName: string) => void;
  onDisallowedFileSelect: (fileName: string) => void;
}

const EvidenceFileUpload = (props: EvidenceFileUploadProps) => {
  const [filesState, setFilesState] = React.useState<Map<string, FileStatue>>(
    new Map<string, FileStatue>()
  );

  const uploadRef = React.useRef<FileUpload>(null);

  const onSelect = React.useCallback((event: FileUploadSelectEvent) => {
    const newMap: Map<string, FileStatue> = new Map();
    const files: FileUploadFile[] = [];
    event.files.forEach(f => {
      if (!isAllowedFile(f.name)) {
        props.onDisallowedFileSelect(f.name);
      } else {
        newMap.set(f.name, FileStatue.Pending);
        files.push(f);
      }
    })

    if (uploadRef.current) {
      uploadRef.current.setFiles(files);
    }
    setFilesState(newMap);
  }, [props]);

  const onRemove = React.useCallback(
    (fileName: string, onRemove: Function) => {
      setFilesState((preState) => {
        const newMap = new Map(preState);
        newMap.delete(fileName);
        return newMap;
      });
      onRemove();
    },
    []
  );

  const upload = React.useCallback(
    async (file: FileUploadFile, hex: string) => {
      const detail: FileUploadDetails = {
        originalFileName: file.name,
        originalFileHash: [
          {
            hashType: "crc-32",
            value: hex,
          },
        ],
      };

      uploadFile(props.issueId ?? "", detail, file)
        .then((response) => {
          props.onUploadCompleted(file.name);
          setFilesState((preState) => {
            const newMap = new Map(preState);
            newMap.set(file.name, FileStatue.Completed);
            return newMap;
          });
        })
        .catch((error) => {
          props.onUploadError(file.name);
          setFilesState((preState) => {
            const newMap = new Map(preState);
            newMap.set(file.name, FileStatue.Fail);
            return newMap;
          });
        });
    },
    [props]
  );

  const uploadHandler = React.useCallback(
    async (event: FileUploadHandlerEvent) => {
      event.files.forEach((file) => {
        if (!file) return;

        const reader = new FileReader();
        reader.onload = async () => {
          const arrayBuffer = reader.result as ArrayBuffer;
          const uint8Array = new Uint8Array(arrayBuffer);
          const crc = CRC32.buf(uint8Array) >>> 0;
          const hex = crc.toString(16).padStart(8, "0");
          upload(file, hex);
        };

        setFilesState((preState) => {
          const newMap = new Map(preState);
          newMap.set(file.name, FileStatue.Uploading);
          return newMap;
        });

        reader.readAsArrayBuffer(file);
      });
    },
    [upload]
  );

  const filePreview = React.useCallback((file: FileUploadFile) => {
    const type = getFileType(file.name);
    return type === "Image" ? (
      <img
        alt={file.name}
        role="presentation"
        src={file.objectURL}
        width={90}
        height={90}
      />
    ) : (
      <div style={{ width: '90px', height: '90px' }}/>
    );
  }, []);

  const fileStateTagSeverity = React.useCallback(
    (fileName: string) => {
      const state = filesState.get(fileName);
      switch (state) {
        case FileStatue.Pending:
          return "warning";
        case FileStatue.Uploading:
          return "info";
        case FileStatue.Completed:
          return "success";
        case FileStatue.Fail:
          return "danger";
        default:
          return "secondary";
      }
    },
    [filesState]
  );

  const itemTemplate = (inFile: object, props: ItemTemplateOptions) => {
    const file = inFile as FileUploadFile;

    return (
      <div className="flex flex-row align-items-center" style={{ padding: '48px 24px 48px 24px' }}>
        <div className="flex flex-row align-items-center" style={{ gap: '16px' }}>
          {filePreview(file)}
          <div id="file-info" className="flex flex-column align-items-start" style={{ gap: '12px' }}>
            <span style={{ maxWidth: '200px', overflowWrap: 'break-word' }}>
              {truncateFileName(file.name)}
            </span>
            <span>
              {formatBytes(file.size)}
            </span>
            <Tag
              style={{ width: '70px', height: '26px', borderRadius: '8px' }}
              value={FileStatue[filesState.get(file.name) ?? 4]}
              severity={fileStateTagSeverity(file.name)}
            />
          </div>
        </div>
        <Button
          type="button"
          icon="pi pi-times"
          className="p-button-outlined p-button-rounded p-button-danger ml-auto"
          onClick={() => onRemove(file.name, props.onRemove)}
        />
      </div>
    );
  };

  return (
    <FileUpload
      ref={uploadRef}
      multiple
      customUpload={true}
      uploadHandler={uploadHandler}
      itemTemplate={itemTemplate}
      emptyTemplate={
        <span className="flex justify-content-center">Drag and drop files to here to upload.</span>
      }
      onSelect={onSelect}
      chooseOptions={chooseOptions}
      uploadOptions={uploadOptions}
      cancelOptions={cancelOptions}
      pt={FileUploadPtOptions}
    />
  );
};

export default EvidenceFileUpload;
