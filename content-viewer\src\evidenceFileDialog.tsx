import { Dialog } from "primereact/dialog";
import { Image } from "primereact/image";
import { Content } from "./dataModel";
import VideoPlayer from "./videoPlayer";
import { InputTextarea } from "primereact/inputtextarea";
import React from "react";
import { getTextFileContent } from "./api";
import { getFileType } from "./utility";

interface EvidenceViewDialogProps {
  visible: boolean;
  onHide: () => void;
  issueId: string;
  content: Content;
}

const EvidenceFileDialog = (props: EvidenceViewDialogProps) => {
  const { fileName, signedUrl } = props.content;

  const [textContent, setTextContent] = React.useState("Loading the content");
  const fileType = getFileType(fileName);

  React.useEffect(() => {
    if (fileType === "Text") {
      getTextFileContent(props.issueId, props.content.fileName)
        .then(content => setTextContent(content ?? "Fail to fetch the content"))
        .catch(error => console.error(error));
    }
  }, [fileName, fileType, props.content.fileName, props.content.signedUrl, props.issueId]);

  const body = () => {
    if (fileType === "Image") {
      return <Image src={signedUrl} />;
    } else if (fileType === "Text") {
      return <InputTextarea value={textContent} readOnly autoResize style={{ width: '100%' }} />;
    } else {
      return <VideoPlayer type={fileType} m3u8Url={props.content.signedUrl} />;
    }
  };

  return (
    <Dialog header={fileName} visible={props.visible} style={{ width: "50vw" }} onHide={props.onHide}>
      {body()}
    </Dialog>
  );
}

export default EvidenceFileDialog;