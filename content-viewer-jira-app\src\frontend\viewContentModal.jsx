import { invoke } from '@forge/bridge';
import { <PERSON>ton, LinkButton, ButtonGroup, Modal, ModalBody, Modal<PERSON>ooter, ModalHeader, ModalTitle, ModalTransition, TextArea } from "@forge/react";
import React from "react";

const ViewContentModal = (props) => {
  const { isOpen, closeModal, issueId, fileName, signedUrl } = props;

  const [content, setContent] = React.useState("Loading the file");

  React.useEffect(() => {
    if (issueId === undefined || fileName === undefined) {
      return;
    }

    invoke('getTextFileContent', { issueId: issueId, key: fileName }).then(data => {
      setContent(data);
    }).catch(error => {
      console.error("Failed to getTextFileContent", error)
    });
  }, [issueId, fileName]);

  return (
    <ModalTransition>
      {isOpen && (
        <Modal onClose={closeModal}>
          <ModalHeader>
            <ModalTitle>{fileName}</ModalTitle>
          </ModalHeader>
          <ModalBody>
            <TextArea
              id="area"
              isReadOnly={true}
              resize="smart"
              value={content}
            />
          </ModalBody>
          <ModalFooter>
            <ButtonGroup label="Default button group">
              <LinkButton href={signedUrl} appearance="subtle-link">Download</LinkButton>
              <Button onClick={closeModal} appearance="primary">Close</Button>
            </ButtonGroup>
          </ModalFooter>
        </Modal>
      )}
    </ModalTransition>
  );
};

export default ViewContentModal;
