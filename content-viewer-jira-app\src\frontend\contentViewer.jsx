import { useProductContext, Link<PERSON>utton, Text } from "@forge/react";
import { invoke } from '@forge/bridge';
import React from "react";
import EvidenceTable from './evidenceTable';

function ViewContentViewer() {
  const [files, setFiles] = React.useState([]);
  const [externalViewUrl, setExternalViewUrl] = React.useState("");
  const [isDataReady, setIsDataReady] = React.useState(false);

  const context = useProductContext();
  const userEmail = React.useRef('');

  React.useEffect(() => {
    if (context === undefined) {
      return;
    }

    let isFilesFetched = false;
    let isExternalViewUrlFetched = false;

    try {
      invoke('getEvidenceUrls', { issueId: context.extension.issue.key }).then(response => {
        setFiles(response);
        isFilesFetched = true;
        if (isExternalViewUrlFetched) {
          setIsDataReady(true);
        }
      }).catch(error => {
        console.error("Failed to getEvidenceUrls", error)
      });

      invoke('getEnv', {}).then(env => {
        setExternalViewUrl(`${env.APP_HOST}/evidenceList/${context.extension.issue.key}`);
        isExternalViewUrlFetched = true;
        if (isFilesFetched) {
          setIsDataReady(true);
        }
      }).catch(error => {
        console.error("Failed to getEnv", error)
      });

      invoke('getUserEmail', {}).then(email => {
        userEmail.current = email;
      }).catch(error => {
        console.error("Failed to getUserEmail", error)
      });

    } catch (error) {
      console.error("Failed to fetch data", error);
      setIsDataReady(true);
    }
  }, [context]);

  return (
    <>
      {!isDataReady ? (
        <Text>Loading data…</Text>
      ) : (
        <>
          <EvidenceTable files={files} issueId={context?.extension.issue.key} />
          <LinkButton target="_blank" href={externalViewUrl} appearance="primary">View More Evidence</LinkButton>
        </>
      )}
    </>
  )
}

export default ViewContentViewer;
