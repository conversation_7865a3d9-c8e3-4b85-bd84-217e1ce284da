export interface Content {
  fileName: string;
  lastModified: string;
  url: string;
  signedUrl: string;
  fileType: string;
  uploader?: string;
}

interface NameValue {
  name: string;
  value: string;
}

interface DeviceId {
  // ISO 8601 date and time
  dateTime?: string;
  eventName?: string;
  idType: string;
  idValue: string;
}

interface FileAnnotations {
  animeDrawingVirtualHentai?: boolean;
  bestiality?: boolean;
  generativeAi?: boolean;
  infant?: boolean;
  liveStreaming?: boolean;
  physicalHarm?: boolean;
  possibleSelfProduction?: boolean;
  potentialMeme?: boolean;
  violenceGore?: boolean;
  viral?: boolean;
}

interface EstimatedLocation {
  city?: string;
  countryCode?: string;
  region?: string;

  // ISO 8601 date and time
  timestamp?: string;
  verified?: boolean;
}

interface FileAnnotations {
  animeDrawingVirtualHentai?: boolean;
  bestiality?: boolean;
  generativeAi?: boolean;
  infant?: boolean;
  liveStreaming?: boolean;
  physicalHarm?: boolean;
  possibleSelfProduction?: boolean;
  potentialMeme?: boolean;
  violenceGore?: boolean;
  viral?: boolean;
}

interface IpCaptureEvent {
  // ISO 8601 date and time
  dateTime?: string;
  eventName?: string;
  ipAddress: string;
  port?: number;
  possibleProxy?: boolean;
}

interface Hash {
  hashType: string;
  value: string;
}

export interface FileUploadDetails {
  additionalInfo?: string;
  details?: NameValue[];
  deviceId?: DeviceId[];
  exifViewedByEsp?: boolean;
  fileAnnotations?: FileAnnotations;
  fileName?: string;
  fileRelevance?: string;
  fileViewedByEsp?: boolean;
  industryClassification?: string;
  ipCaptureEvent?: IpCaptureEvent;
  locationOfFile?: string;
  originalFileHash: Hash[];
  originalFileName?: string;
  potentialMeme?: boolean;
  publiclyAvailable?: boolean;

  // ISO 8601 date and time
  uploadedToEspTimestamp?: string;
}

export interface FileResponse {
  checksumAlgorithm: string[];
  checksumType: string;
  fileId: string;
  fileName: string;
  key: string;
  lastModified: string;
  size: number;
  tags?: Record<string, string>;
}

export interface AuditLog {
  pk: string;
  sk: string;
  remoteIpAddress: string;
  userId: string;
  eventType: string;
  eventMessage: string;
  eventTime: string;
  metaData: string;

	// Pk              string  `json:"pk"`
	// Sk              string  `json:"sk"`
	// RemoteIpAddress string  `json:"remoteIpAddress"`
	// UserId          *string `json:"userId"`
	// EventType       string  `json:"eventType"`
	// EventMessage    string  `json:"eventMessage"`
	// EventTime       string  `json:"eventTime"`
	// MetaData        *string `json:"metaData"`
}
