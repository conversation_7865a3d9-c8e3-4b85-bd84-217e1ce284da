import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, UpdateCommand, BatchGetCommand } from '@aws-sdk/lib-dynamodb';
import { fromSSO } from "@aws-sdk/credential-provider-sso";
let ddb = undefined;

function getDdbClient() {
  if (ddb === undefined) {
    try {
      const client = new DynamoDBClient({ region: process.env.DDB_REGION });
      if (process.env.IS_LOCAL == "true") {
        client.config.credentials = fromSSO({});
      }
      ddb = DynamoDBDocumentClient.from(client);
    } catch (error) {
      console.error("failed to create a DDB client. Error is", error);
    }
  }
  return ddb;
}

export async function getFromDDB(req, res) {
  const { jiraCloudId, issueId } = req.params;
  const command = new GetCommand({
    TableName: process.env.DYNAMO_TABLE_NAME,
    Key: { CloudIdIssueId: `${jiraCloudId}:${issueId}` },
  });

  try {
    const response = await getDdbClient().send(command);
    res.status(200).send(response.Item ? response.Item.value : '');
  } catch (error) {
    console.error(error);
    res.status(error.$metadata.httpStatusCode).send(error.message);
  }
}

export async function ddbUpdate(req, res) {
  const { fieldName, fieldValue } = req.body;
  const { jiraCloudId, issueId } = req.params;

  if (!fieldName || typeof fieldName !== "string") {
    return res.status(400).send("Invalid fieldName");
  }

  const command1 = new UpdateCommand({
    TableName: process.env.DYNAMO_TABLE_NAME,
    Key: { CloudIdIssueId: `${jiraCloudId}:${issueId}` },
    UpdateExpression: "SET CustomFields = if_not_exists(CustomFields, :emptyMap)",
    ExpressionAttributeValues: { ":emptyMap": {} },
  });

  const command2 = new UpdateCommand({
    TableName: process.env.DYNAMO_TABLE_NAME,
    Key: { CloudIdIssueId: `${jiraCloudId}:${issueId}` },
    UpdateExpression: "SET CustomFields.#fieldName = :fieldValue",
    ExpressionAttributeNames: {
      "#fieldName": fieldName,
    },
    ExpressionAttributeValues: {
      ":fieldValue": fieldValue,
    },
  });

  try {
    let result = await getDdbClient().send(command1);
    result = await getDdbClient().send(command2);
    console.log("DynamoDB update success:", result);
    res.status(200).send();
  } catch (error) {
    console.error("DynamoDB Update Error:", error);
    res.status(error?.$metadata?.httpStatusCode || 500).send(error.message);
  }
}
