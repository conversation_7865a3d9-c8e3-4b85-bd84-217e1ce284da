name: Deploy <PERSON>
on:
  push:
    branches:
      - main
    tags-ignore:
      - '**' 
    paths:
      - media-converter/**
      - splunk-log-forwarder/**
  workflow_dispatch:
  workflow_call:

permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  build-image:
    name: Deploy Lambda
    runs-on: [t2gp-arc-linux]
    environment: develop
    steps:
      - name: Check out code
        uses: actions/checkout@v5
        with:
          fetch-depth: 0

      - name: Environment Variables
        run: |
          if [[ "${{ github.event_name }}" == "workflow_call" ]]; then
            echo AWS_ACCOUNT_ID=$(echo '${{vars.AWS_ACCOUNTS}}' | jq -c '.main.account_id' -r) >> $GITHUB_ENV
            echo AWS_REGION=$(echo '${{vars.AWS_ACCOUNTS}}' | jq -c '.main.region' -r) >> $GITHUB_ENV
          else
            echo "DEV_ACCOUNT=non-" >> $GITHUB_ENV
            echo AWS_ACCOUNT_ID=$(echo '${{vars.AWS_ACCOUNTS}}' | jq -c '.dev.account_id' -r) >> $GITHUB_ENV
            echo AWS_REGION=$(echo '${{vars.AWS_ACCOUNTS}}' | jq -c '.dev.region' -r) >> $GITHUB_ENV
          fi

      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::${{env.AWS_ACCOUNT_ID}}:role/github_actions_admin
          role-session-name: ${{ vars.GH_AWS_SESSION }}
          aws-region: ${{env.AWS_REGION}}

      - name: Use Node.js 22.14.0
        uses: actions/setup-node@v4
        with:
          node-version: 22.14.0
          registry-url: https://npm.pkg.github.com
          scope: take-two-t2gp

      - name: Detect folder changed
        if: github.event_name != 'workflow_call'
        id: detect
        run: |
          CHANGED=$(git diff --name-only ${{ github.event.before }} ${{ github.sha }})
          if echo "$CHANGED" | grep -q "^splunk-log-forwarder/"; then
            echo "splunk=true" >> $GITHUB_OUTPUT
          fi
          if echo "$CHANGED" | grep -q "^media-converter/"; then
            echo "media=true" >> $GITHUB_OUTPUT
          fi
      
      # Install Node.js dependencies & deploy Splunk lambda
      - name: Deploy Splunk Lambda
        if: steps.detect.outputs.splunk == 'true' || github.event_name == 'workflow_call'
        run: |
          cd splunk-log-forwarder
          npm install --save-dev typescript
          npx tsc && \
          cp -r node_modules dist/ && \
          cd dist && zip -r ../splunk.zip . && cd .. && \
          aws lambda update-function-code \
            --function-name t2gp-social-${{ env.DEV_ACCOUNT }}production-LegalAPILambdaFW \
            --zip-file fileb://splunk.zip

      # Package & deploy Media Converter (Python lambda)
      - name: Deploy Media Lambda
        if: steps.detect.outputs.media == 'true' || github.event_name == 'workflow_call'
        run: |
          cd media-converter
          zip -r ../media.zip .
          aws lambda update-function-code \
            --function-name t2gp-social-${{ env.DEV_ACCOUNT }}production-SubmitMediaConvertJob \
            --zip-file fileb://../media.zip
