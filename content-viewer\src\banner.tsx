import React from "react";
import { useAuth } from "./authContext";
import { logout } from "./api";
import { Button } from "primereact/button";
import { BannerRootStyle, LogoutButtonStyle } from "./style";
import { useNavigate } from "react-router-dom";

const Banner = () => {
  const auth = useAuth();
  const navigate = useNavigate();
  
  const onClickLogout = () => {
    logout()
      .then(() => {
        auth.setIsLoggedIn(false);
        navigate('/');
      })
      .catch(error => console.error(error));
  };

  const logoutButton = () => {
    if (!auth.isLoggedIn) {
      return <></>;
    }
    return <Button label="Sign out" style={LogoutButtonStyle} onClick={onClickLogout} />;
  };

  return (
    <div style={BannerRootStyle}>
      <img src="/logo.png" alt="T2GP Logo" width={142} height={48} />
      {logoutButton()}
    </div>
  );
};

export default Banner;
