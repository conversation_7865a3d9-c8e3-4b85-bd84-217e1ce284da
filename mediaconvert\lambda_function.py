import boto3
import os
import logging

logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event, context):
    logger.info("Event: %s", event)

    # Extract info from S3 event
    try:
        s3_record = event['Records'][0]['s3']
        bucket = s3_record['bucket']['name']
        key = s3_record['object']['key']
    except Exception as e:
        logger.error("Error parsing event: %s", e)
        raise e

    # Env variables
    region = os.environ.get('AWS_REGION', 'us-east-1')
    role_arn = os.environ['MEDIACONVERT_ROLE']
    queue_arn = os.environ.get('MEDIACONVERT_QUEUE')

    # Create MediaConvert client (regional)
    mediaconvert = boto3.client('mediaconvert', region_name=region)

    # Set output destination
    output_prefix = "output/hls/"
    destination = f"s3://{bucket}/{output_prefix}"

    try:
        response = mediaconvert.create_job(
            Role=role_arn,
            Queue=queue_arn,
            Settings={
                "Inputs": [{
                    "FileInput": f"s3://{bucket}/{key}",
                    "AudioSelectors": {
                        "Audio Selector 1": {
                            "DefaultSelection": "DEFAULT"
                        }
                    }
                }],
                "OutputGroups": [{
                    "Name": "HLS Group",
                    "OutputGroupSettings": {
                        "Type": "HLS_GROUP_SETTINGS",
                        "HlsGroupSettings": {
                            "Destination": destination,
                            "SegmentLength": 6,
                            "MinSegmentLength": 1,
                            "ManifestDurationFormat": "INTEGER",
                            "OutputSelection": "MANIFESTS_AND_SEGMENTS",
                            "StreamInfResolution": "INCLUDE"
                        }
                    },
                    "Outputs": [
                        {
                            "NameModifier": "_720p",
                            "ContainerSettings": {
                                "Container": "M3U8"
                            },
                            "VideoDescription": {
                                "Width": 1280,
                                "Height": 720,
                                "CodecSettings": {
                                    "Codec": "H_264",
                                    "H264Settings": {
                                        "Bitrate": 3000000,
                                        "RateControlMode": "CBR",
                                        "GopSize": 90,
                                        "GopSizeUnits": "FRAMES",
                                        "GopBReference": "ENABLED",
                                        "HrdBufferSize": 6000000
                                    }
                                }
                            },
                            "AudioDescriptions": [{
                                "CodecSettings": {
                                    "Codec": "AAC",
                                    "AacSettings": {
                                        "Bitrate": 96000,
                                        "CodingMode": "CODING_MODE_2_0",
                                        "SampleRate": 48000
                                    }
                                }
                            }]
                        }
                    ]
                }]
            }
        )

        job_id = response['Job']['Id']
        logger.info("MediaConvert HLS job submitted: %s", job_id)
        return {"status": "submitted", "job_id": job_id}

    except Exception as e:
        logger.error("MediaConvert job submission failed: %s", e)
        raise e