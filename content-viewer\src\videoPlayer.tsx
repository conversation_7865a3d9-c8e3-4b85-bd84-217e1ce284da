import React from "react";
import videojs from "video.js";
import Player from "video.js/dist/types/player";
import "video.js/dist/video-js.css";
import { usePresignedUrls } from "./filePresignContext";

interface VideoPlayerProps {
  options?: Player["options"];
  m3u8Url: string;
  type: string;
}

const VideoPlayer = (props: VideoPlayerProps) => {
  const { getPresignedUrl } = usePresignedUrls();

  const videoRef = React.useRef<HTMLDivElement>(null);
  const playerRef = React.useRef<Player | null>(null);
  const { type, m3u8Url: url } = props;
  const applicationType = type === "Video" ? "video/mp4" : "application/x-mpegURL";

  React.useEffect(() => {
    if (!playerRef.current && videoRef.current) {
      const videoElement = document.createElement("video-js");

      videoElement.classList.add("vjs-big-play-centered");
      videoRef.current.appendChild(videoElement);

      playerRef.current = videojs(videoElement, {
        controls: true,
        controlBar: {
          currentTimeDisplay: true,
          timeDivider: true,
          durationDisplay: true,
          remainingTimeDisplay: false
        },
        fluid: true,
        preload: 'auto',
        sources: [{ src: url, type: applicationType }],
        ...props.options,
      });

      playerRef.current.on('xhr-hooks-ready', () => {
        (videojs as any).Vhs.xhr.beforeRequest = (options: any) => {
          console.log('Original URI:', options.uri);

          if (options.uri.endsWith('.ts')) {
            const segmentName = options.uri.split('/').pop();
            const signedUrl = getPresignedUrl(segmentName);
            options.uri = signedUrl;
          }

          return options;
        };

      });
    }
  }, [applicationType, props.options, url, getPresignedUrl]);

  React.useEffect(() => {
    const player = playerRef.current;

    return () => {
      if (player && !player.isDisposed()) {
        player.dispose();
        playerRef.current = null;
      }
    };
  }, [playerRef]);

  return <div ref={videoRef} />;
}

export default VideoPlayer;