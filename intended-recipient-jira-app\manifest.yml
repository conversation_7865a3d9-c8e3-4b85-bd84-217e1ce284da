modules:
  jira:issueContext:
    - key: intended-recipient-jira-app-issue-context
      resource: main
      resolver:
        function: resolver
      render: native
      title: Intended Recipient Information
      description: Intended Recipient Information
      label: Intended Recipient Information
  function:
    - key: resolver
      handler: index.handler
resources:
  - key: main
    path: src/frontend/index.jsx
app:
  runtime:
    name: nodejs22.x
    memoryMB: 256
    architecture: arm64
  id: ari:cloud:ecosystem::app/520d7e45-9ecb-40df-978d-084ff35c69b8
permissions:
  scopes:
    - storage:app
    - read:jira-user
  external:
    fetch:
      backend:
        - 'https://t2gp-social-legal-api.dev.d2dragon.net/*'