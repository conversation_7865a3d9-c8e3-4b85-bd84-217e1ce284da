import boto3
import os
import logging
import time
import json
import urllib.request
from botocore.exceptions import ClientError

logger = logging.getLogger()
logger.setLevel(logging.INFO)
s3 = boto3.client('s3')

MAX_RETRIES = 2
RETRY_DELAY = 3  # seconds between retries 

VALID_EXTENSIONS = ('.mp4', '.mov', '.avi', '.wmv', '.mkv', '.webm')

def notify_event(ticket_id, status, message):
    legal_api_url = os.environ["LEGAL_API_URL"]
    url = f"{legal_api_url}/{ticket_id}/events"
    logger.info("Notification sent: %s", url)
    payload = json.dumps({
        "eventName": status,
        "eventMessage": message
    }).encode('utf-8')

    headers = {
        "Content-Type": "application/json"
    }

    req = urllib.request.Request(url, data=payload, headers=headers, method='POST')

    try:
        with urllib.request.urlopen(req) as resp:
            logger.info("Notification sent: %s", resp.status)
    except Exception as e:
        logger.error("Failed to notify legal-api: %s", e)

def tag_object(bucket, key, tags):
    tagging = {
        'TagSet': [{'Key': k, 'Value': v} for k, v in tags.items()]
    }
    try:
        s3.put_object_tagging(Bucket=bucket, Key=key, Tagging=tagging)
        logger.info(f"Tagged object {key} in {bucket} with {tags}")
    except ClientError as e:
        logger.error(f"Failed to tag object {key}: {e}")

def tag_related_hls_files(bucket, key, tags):
    prefix = "/".join(key.split("/")[:-1]) + "/"
    paginator = s3.get_paginator('list_objects_v2')

    for page in paginator.paginate(Bucket=bucket, Prefix=prefix):
        for obj in page.get('Contents', []):
            obj_key = obj['Key']
            if obj_key.lower().endswith(".m3u8") or obj_key.lower().endswith(".ts"):
                tag_object(bucket, obj_key, tags)

def lambda_handler(event, context):
    logger.debug("Received event: %s", event)

    for record in event.get("Records", []):
        try:
            s3_record = record['s3']
            bucket = s3_record['bucket']['name']
            key = s3_record['object']['key']
        except Exception as e:
            logger.error("Error parsing S3 event: %s", e)
            continue

        try:
            ticket_id = key.split('/')[1]
        except Exception:
            ticket_id = "unknown"
        
        tags = {
            "evidenceViewerCanFetch": "true",
            "submitToNcmec": "false",
            "originalFile": "false"
        }

        file_name = key.lower()
        # If .m3u8 file is detected, treat it as a completed MediaConvert output
        if file_name.endswith('.m3u8') and '-hls' not in file_name:
            ticket_id = key.split('/')[1]
            tag_related_hls_files(bucket, key, tags) 
            logger.info("Detected .m3u8 file upload: %s", key)
            notify_event(ticket_id, "completed", f"MediaConvert output ready: {key}")
            return {"status": "notified", "file": key}

        if not file_name.endswith(VALID_EXTENSIONS):
            # silently skip without logging
            return {"status": "skipped", "reason": "unsupported file type"}

        # Submit job to MediaConvert
        logger.info("Submitting MediaConvert job for: %s", key)

        region = os.environ.get('AWS_REGION', 'us-east-1')
        role_arn = os.environ['MEDIACONVERT_ROLE']
        queue_arn = os.environ.get('MEDIACONVERT_QUEUE')
        destination = f"s3://{bucket}/{key.rsplit('/', 1)[0]}/"
        logger.debug(f"Using destination: {destination}")

        # Initialize MediaConvert with the correct endpoint
        mediaconvert = boto3.client('mediaconvert', region_name=region)
        endpoint_url = mediaconvert.describe_endpoints()['Endpoints'][0]['Url']
        mediaconvert = boto3.client('mediaconvert', region_name=region, endpoint_url=endpoint_url)

        # MediaConvert job settings
        settings = {
            "Inputs": [{
                "FileInput": f"s3://{bucket}/{key}",
                "AudioSelectors": {
                    "Audio Selector 1": {
                        "DefaultSelection": "DEFAULT"
                    }
                }
            }],
            "OutputGroups": [{
            "Name": "HLS Group",
            "OutputGroupSettings": {
                "Type": "HLS_GROUP_SETTINGS",
                "HlsGroupSettings": {
                    "Destination": destination,
                    "SegmentLength": 30,
                    "MinSegmentLength": 0,
                    "ManifestDurationFormat": "INTEGER",
                    "OutputSelection": "MANIFESTS_AND_SEGMENTS",
                    "StreamInfResolution": "INCLUDE"
                }
            },
            "Outputs": [{
                "NameModifier": "-hls",
                "ContainerSettings": {
                    "Container": "M3U8"
                },
                "VideoDescription": {
                    "CodecSettings": {
                        "Codec": "H_264",
                        "H264Settings": {
                            "Bitrate": 3000000,
                            "RateControlMode": "CBR",
                            "GopSize": 90,
                            "GopSizeUnits": "FRAMES",
                            "GopBReference": "ENABLED",
                            "HrdBufferSize": 6000000
                        }
                    }
                },
                "AudioDescriptions": [{
                    "AudioTypeControl": "FOLLOW_INPUT",
                    "CodecSettings": {
                        "Codec": "AAC",
                        "AacSettings": {
                            "Bitrate": 96000,
                            "CodingMode": "CODING_MODE_2_0",
                            "SampleRate": 48000
                        }
                    },
                    "LanguageCodeControl": "FOLLOW_INPUT"
                }]
            }]
        }]
    }

    tags = {
            "evidenceViewerCanFetch": "true",
            "submitToNcmec": "false",
            "originalFile": "false"
        }

    # Submit job with retries
    for attempt in range(MAX_RETRIES + 1):
        try:
            response = mediaconvert.create_job(
                Role=role_arn,
                Queue=queue_arn,
                Settings=settings,
                Tags=tags,
                UserMetadata={"ticket_id": ticket_id}
            )

            job_id = response['Job']['Id']
            logger.info("MediaConvert job submitted: %s", job_id)
            notify_event(ticket_id, "success", f"MediaConvert job submitted: {job_id}")
            break  # Exit retry loop
        except ClientError as e:
            logger.error("Attempt %d failed: %s", attempt + 1, e)
            notify_event(ticket_id, "info", f"Attempt {attempt + 1} failed: {job_id}")
            if attempt == MAX_RETRIES:
                notify_event(ticket_id, "error", f"MediaConvert job failed after {MAX_RETRIES + 1} attempts")
                raise e
            time.sleep(RETRY_DELAY)

    return {"status": "done"}