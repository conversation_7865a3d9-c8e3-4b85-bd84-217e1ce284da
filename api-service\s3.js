import {
  GetObjectCommand,
  ListObjectsV2Command,
  S3Client,
} from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { fromSSO } from "@aws-sdk/credential-provider-sso";

const region = "us-east-1";
let s3Client = undefined;

function getS3Client() {
  if (s3Client === undefined) {
    try {
      s3Client = new S3Client({
        region: region,
      });

      if (process.env.IS_LOCAL == "true") {
        s3Client.config.credentials = fromSSO({});
      }
    } catch (error) {
      console.error("failed to create an S3 client. Error is", error);
    }
  }
  return s3Client;
}

function getFileNameFromKey(key) {
  return key.split("/").pop() || "";
}

export async function getItemUrlsFromS3(req, res) {
  const client = getS3Client();

  if (client === undefined) {
    console.error("failed to create an S3 client");
    return;
  }

  const prefix = `${
    process.env.S3_BUCKET_FOLDER
  }/${req.params.jiraIssueId.toUpperCase()}`;
  const command = new ListObjectsV2Command({
    Bucket: process.env.S3_BUCKET,
    Prefix: prefix,
    MaxKeys: "1000",
  });

  try {
    const response = await client.send(command);
    if (response.Contents === undefined || response.Contents.length === 0) {
      console.log("No objects found.");
      return [];
    }

    const items = response.Contents.filter(
      (o) =>
        // Folder
        o.Size > 0 &&
        // Detail files
        !o.Key.includes("/details/") &&
        // Ts files
        !o.Key.endsWith(".ts")
    );
    console.log("Found %d objects", items.length);

    const results = await Promise.all(
      items.map(async (obj) => {
        const input = {
          Bucket: process.env.S3_BUCKET,
          Key: obj.Key,
        };

        if (obj.Key.endsWith(".txt")) {
          const fileName = getFileNameFromKey(obj.Key);
          input.ResponseContentDisposition = `attachment; filename="${fileName}"`;
          input.ResponseContentType = "text/plain";
        }

        const signedUrl = await getSignedUrl(
          client,
          new GetObjectCommand(input),
          {
            expiresIn: 60 * 60 /* 1 hour */,
          }
        );

        return {
          key: obj.Key,
          url: `${process.env.FILE_HOST}/${obj.Key}`,
          lastModified: obj.LastModified,
          signedUrl: signedUrl,
        };
      })
    );
    res.status(200).send(results);
  } catch (error) {
    res.status(error.$metadata.httpStatusCode).send(error.message);
    console.error(error);
  }
}

export async function getTextFileFromS3(req, res) {
  const client = getS3Client();

  if (client === undefined) {
    console.error("failed to create an S3 client");
    return;
  }

  const key = `${
    process.env.S3_BUCKET_FOLDER
  }/${req.params.jiraIssueId.toUpperCase()}/${req.params.fileKey}`;
  const command = new GetObjectCommand({
    Bucket: process.env.S3_BUCKET,
    Key: key,
  });

  try {
    const response = await client.send(command);
    const str = await response.Body.transformToString();
    response.Body.destroy();
    res.status(200).send(str);
  } catch (error) {
    res.status(error.$metadata.httpStatusCode).send(error.message);
    console.error(error);
  }
}
