import React, { useEffect, useState } from 'react';
import ForgeRecon<PERSON><PERSON>, { useProductContext, Icon, Inline, Stack, Textfield, Label, Tabs, TabList, Tooltip, Tab, TabPanel, Checkbox } from '@forge/react';
import { invoke } from '@forge/bridge';
import { LocationInfo } from './estimatedLocation'
import { DeviceEventFields } from './device';
import { PersonFields } from './person';
import { IpCaptureEventFields } from './ipCaptureEvent';
import { AccountDisabledFields } from './disabledAccount';

const App = () => {
  const [schoolName, setSchoolName] = useState('')
  const [espService, setEspService] = useState('')
  const [screenName, setScreenName] = useState('')
  const [displayName, setDisplayName] = useState('')
  const [profileUrl, setProfileUrl] = useState('')
  const [profileBio, setProfileBio] = useState('')
  const [espIdentifer, setEspIdentifer] = useState('')
  const [compromisedAccount, setCompromisedAccount] = useState(false);
  const [groupIdentifier, setGroupIdentifier] = useState('');
  const [thirdPartyUserReported, setThirdPartyUserReported] = useState(false);
  const [priorCTReports, setPriorCTReports] = useState('');
  const [additionalInfo, setAdditionalInfo] = useState('');

  const context = useProductContext();

  const onChangeSchoolName = (e) => {
    setSchoolName(e.target.value);
  };

  const onBlurSchoolName = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setSchoolName', { cloudId: context.cloudId, issueId: key, schoolName: e.target.value });
  };

  const onChangeEspService = (e) => {
    setEspService(e.target.value);
  };

  const onBlurEspService = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setEspService', { cloudId: context.cloudId, issueId: key, espService: e.target.value });
  };

  const onChangeScreenName = (e) => {
    setScreenName(e.target.value);
  };

  const onBlurScreenName = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setScreenName', { cloudId: context.cloudId, issueId: key, screenName: e.target.value });
  };

  const onChangeDisplayName = (e) => {
    setDisplayName(e.target.value);
  };

  const onBlurDisplayName = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setDisplayName', { cloudId: context.cloudId, issueId: key, displayName: e.target.value });
  };

  const onChangeProfileUrl = (e) => {
    setProfileUrl(e.target.value);
  };

  const onBlurProfileUrl = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setProfileUrl', { cloudId: context.cloudId, issueId: key, profileUrl: e.target.value });
  };
  const onChangeProfileBio = (e) => {
    setProfileBio(e.target.value);
  };

  const onBlurProfileBio = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setProfileBio', { cloudId: context.cloudId, issueId: key, profileBio: e.target.value });
  };

  const onChangeEspIdentifer = (e) => {
    setEspIdentifer(e.target.value);
  };

  const onBlurEspIdentifer = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setEspIdentifer', { cloudId: context.cloudId, issueId: key, espIdentifer: e.target.value });
  };

  const onChangeCompromisedAccount = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    const value = e.target.checked;
    setCompromisedAccount(value);
    invoke('setCompromisedAccount', { cloudId: context.cloudId, issueId: key, compromisedAccount: value });
  };

  const onChangeGroupIdentifier = (e) => {
    setGroupIdentifier(e.target.value);
  };

  const onBlurGroupIdentifier = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setGroupIdentifier', { cloudId: context.cloudId, issueId: key, groupIdentifier: e.target.value });
  };

  const onChangeThirdPartyUserReported = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    const value = e.target.checked;
    setThirdPartyUserReported(value);
    invoke('setThirdPartyUserReported', { cloudId: context.cloudId, issueId: key, thirdPartyUserReported: value });
  };

  const onChangePriorCTReports = (e) => {
    setPriorCTReports(e.target.value);
  };

  const onBlurPriorCTReports = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setPriorCTReports', { cloudId: context.cloudId, issueId: key, priorCTReports: e.target.value });
  };

  const onChangeAdditionalInfo = (e) => {
    setAdditionalInfo(e.target.value);
  };

  const onBlurAdditionalInfo = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setAdditionalInfo', { cloudId: context.cloudId, issueId: key, additionalInfo: e.target.value });
  };

  useEffect(() => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;

    const { key } = context.extension.issue;

    const fetchPersonData = async () => {
      try {
        const [
          SchoolName,
          espService,
          screenName,
          displayName,
          profileUrl,
          profileBio,
          espIdentifer,
          compromisedAccount,
          groupIdentifier,
          thirdPartyUserReported,
          priorCTReports,
          additionalInfo
        ] = await Promise.all([
          invoke('getSchoolName', { issueId: key }),
          invoke('getEspService', { issueId: key }),
          invoke('getScreenName', { issueId: key }),
          invoke('getDisplayName', { issueId: key }),
          invoke('getProfileUrl', { issueId: key }),
          invoke('getProfileBio', { issueId: key }),
          invoke('getEspIdentifer', { issueId: key }),
          invoke('getCompromisedAccount', { issueId: key }),
          invoke('getGroupIdentifier', { issueId: key }),
          invoke('getThirdPartyUserReported', { issueId: key }),
          invoke('getPriorCTReports', { issueId: key }),
          invoke('getAdditionalInfo', { issueId: key }),
        ]);

        setSchoolName(SchoolName);
        setEspService(espService);
        setScreenName(screenName);
        setDisplayName(displayName);
        setProfileUrl(profileUrl);
        setProfileBio(profileBio);
        setEspIdentifer(espIdentifer);
        setCompromisedAccount(compromisedAccount);
        setGroupIdentifier(groupIdentifier);
        setThirdPartyUserReported(thirdPartyUserReported);
        setPriorCTReports(priorCTReports);
        setAdditionalInfo(additionalInfo);

      } catch (error) {
        console.error('Failed to fetch reported person data:', error);
      }
    };

    fetchPersonData();
  }, [context]);

  const reportedPersonTab = () => {
    return (
      <Stack alignInline='stretch' grow='fill'>
        <PersonFields context={context} />
        
        <Label labelFor="schoolName">School Name</Label>
        <Textfield id="schoolName" value={schoolName} onChange={onChangeSchoolName} onBlur={onBlurSchoolName} maxLength={255} />
        
        <Inline alignInline="start" space='space.050'>
          <Label labelFor="espService">ESP Service</Label>
          <Tooltip content="The name of the reporter’s product or service that was used by the reported person or user during the incident.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="espService" value={espService} onChange={onChangeEspService} onBlur={onBlurEspService} maxLength={100} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="screenName">Screen Name</Label>
          <Tooltip content="The screen name of the reported person or user.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="screenName" value={screenName} onChange={onChangeScreenName} onBlur={onBlurScreenName} maxLength={256} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="displayName">Display Name</Label>
          <Tooltip content="A display name, other than a screen name or a username, for the reported person or user.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="displayName" value={displayName} onChange={onChangeDisplayName} onBlur={onBlurDisplayName} maxLength={256} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="profileUrl">Profile URL</Label>
          <Tooltip content="An identifying URL for the reported person or user.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="profileUrl" value={profileUrl} onChange={onChangeProfileUrl} onBlur={onBlurProfileUrl} maxLength={2083} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="profileBio">Profile Bio</Label>
          <Tooltip content="A copy of the user’s bio as per their account according to the reporting company’s platform.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="profileBio" value={profileBio} onChange={onChangeProfileBio} onBlur={onBlurProfileBio} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="espIdentifier">ESP Identifier</Label>
          <Tooltip content="The unique ID of the reported person or user in the reporter’s system.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="espIdentifier" value={espIdentifer} onChange={onChangeEspIdentifer} onBlur={onBlurEspIdentifer} maxLength={256} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="compromisedAccount">Compromised Account</Label>
          <Tooltip content="This account has been compromised, hacked, or hijacked.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Checkbox id="compromisedAccount" isChecked={compromisedAccount} onChange={onChangeCompromisedAccount} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="groupIdentifier">Group Identifier</Label>
          <Tooltip content="Unique group identifiers (e.g., group name, group ID) if the reporter believes the reported person or user is engaged in an online group related to child sexual exploitation.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield id="groupIdentifier" value={groupIdentifier} onChange={onChangeGroupIdentifier} onBlur={onBlurGroupIdentifier} maxLength={256} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="thirdPartyUserReported">Third Party User Reported</Label>
          <Tooltip content="Whether the reported person or user is using another company’s service and the reporting company has no further information about this person or user.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Checkbox id="thirdPartyUserReported" isChecked={thirdPartyUserReported} onChange={onChangeThirdPartyUserReported} />

        <Inline alignInline="start" space='space.050'>
          <Label labelFor="priorCTReports">Prior CT Reports</Label>
          <Tooltip content="A report ID for a prior CyberTipline report on this reported person or user.">
            <Icon size='small' glyph="info" label="info" primaryColor="color.icon.information" />
          </Tooltip>
        </Inline>
        <Textfield type='number' id="priorCTReports" value={priorCTReports} onChange={onChangePriorCTReports} onBlur={onBlurPriorCTReports} />

        <Label labelFor="additionalInfo">Additional Info</Label>
        <Textfield id="additionalInfo" value={additionalInfo} onChange={onChangeAdditionalInfo} onBlur={onBlurAdditionalInfo} />
      </Stack>
    )
  }

  const reportedPersonContactTab = () => {
    return (
      <Stack alignInline='stretch' grow='fill'>
        <LocationInfo context={context} />
        <DeviceEventFields context={context} />
      </Stack>
    )
  }

  return (
    <Tabs id="reportedPerson-tabs">
      <TabList>
        <Tab>Reported Person</Tab>
        <Tab>Additional Information</Tab>
        <Tab>IP Event</Tab>
        <Tab>User Account</Tab>
      </TabList>
      <TabPanel>{reportedPersonTab()}</TabPanel>
      <TabPanel>{reportedPersonContactTab()}</TabPanel>
      <TabPanel>
        <Stack alignInline='stretch' grow='fill'>
          <IpCaptureEventFields context={context} />
        </Stack>
      </TabPanel>
      <TabPanel>
        <Stack alignInline='stretch' grow='fill'>
          <AccountDisabledFields context={context} />
        </Stack>
      </TabPanel>
    </Tabs>
  );
};

ForgeReconciler.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
