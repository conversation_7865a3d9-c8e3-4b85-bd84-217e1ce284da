export function getFileType(fileName: string) {
  if (fileName === undefined) {
    return "Unknown";
  }

  // @ts-ignore
  const extension = fileName.split(".").pop().toLowerCase();

  const imageExtensions = ["jpg", "jpeg", "png", "gif"];
  const videoExtensions = ["mp4", "mkv"];
  const streamExtensions = ["m3u8"];
  const textExtensions = ["txt", "json", "xml"];

  if (imageExtensions.includes(extension)) {
    return "Image";
  } else if (videoExtensions.includes(extension)) {
    return "Video";
  } else if (textExtensions.includes(extension)) {
    return "Text";
  } else if (streamExtensions.includes(extension)) {
    return "Stream";
  } else {
    return "Unknown";
  }
}

export function formatTime(isoString: string): string {
  const date = new Date(isoString);
  return (
    date.getUTCFullYear() +
    "-" +
    String(date.getUTCMonth() + 1).padStart(2, "0") +
    "-" +
    String(date.getUTCDate()).padStart(2, "0") +
    " " +
    String(date.getUTCHours()).padStart(2, "0") +
    ":" +
    String(date.getUTCMinutes()).padStart(2, "0") +
    ":" +
    String(date.getUTCSeconds()).padStart(2, "0")
  );
}

const prohibitedExtensions = new Set<string>([
  // Executable / Dangerous files
  ".exe",
  ".dll",
  ".bat",
  ".cmd",
  ".sh",
  ".bash",
  ".ps1",
  ".vbs",
  ".js",
  ".jar",
  ".msi",
  ".com",
  ".scr",
  ".pif",

  // Script / Source code files
  ".php",
  ".py",
  ".rb",
  ".pl",
  ".cgi",
  ".asp",
  ".aspx",
  ".jsp",
  ".tsx",
  ".cs",
  ".java",
  ".cpp",
  ".c",
  ".go",

  // Config / Secrets
  ".env",
  ".gitignore",
  ".yml",
  ".yaml",
  ".ini",
  ".json",
  ".config",
  ".pem",
  ".crt",
  ".key",
  ".cert",

  // System / Hidden
  ".DS_Store",
  ".Thumbs.db",
  ".desktop",
  ".lnk",
]);

export function isAllowedFile(fileName: string): boolean {
  const ext = fileName.slice(fileName.lastIndexOf(".")).toLowerCase();
  return !prohibitedExtensions.has(ext);
}
