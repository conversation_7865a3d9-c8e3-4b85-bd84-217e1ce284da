export function getFileType(fileName: string) {
  if (fileName === undefined) {
    return "Unknown";
  }

  // @ts-ignore
  const extension = fileName.split(".").pop().toLowerCase();

  const imageExtensions = ["jpg", "jpeg", "png", "gif"];
  const videoExtensions = ["mp4", "mkv", "wmv", "avi", "mov"];
  const streamExtensions = ["m3u8"];
  const textExtensions = ["txt", "json", "xml"];

  if (imageExtensions.includes(extension)) {
    return "Image";
  } else if (videoExtensions.includes(extension)) {
    return "Video";
  } else if (textExtensions.includes(extension)) {
    return "Text";
  } else if (streamExtensions.includes(extension)) {
    return "Stream";
  } else {
    return "Unknown";
  }
}

export function formatTime(isoString: string): string {
  const date = new Date(isoString);
  return (
    date.getUTCFullYear() +
    "-" +
    String(date.getUTCMonth() + 1).padStart(2, "0") +
    "-" +
    String(date.getUTCDate()).padStart(2, "0") +
    " " +
    String(date.getUTCHours()).padStart(2, "0") +
    ":" +
    String(date.getUTCMinutes()).padStart(2, "0") +
    ":" +
    String(date.getUTCSeconds()).padStart(2, "0")
  );
}

const prohibitedExtensions = new Set<string>([
  // Executable / Dangerous files
  ".exe",
  ".dll",
  ".bat",
  ".cmd",
  ".sh",
  ".bash",
  ".ps1",
  ".vbs",
  ".js",
  ".jar",
  ".msi",
  ".com",
  ".scr",
  ".pif",

  // Script / Source code files
  ".php",
  ".py",
  ".rb",
  ".pl",
  ".cgi",
  ".asp",
  ".aspx",
  ".jsp",
  ".tsx",
  ".cs",
  ".java",
  ".cpp",
  ".c",
  ".go",

  // Config / Secrets
  ".env",
  ".gitignore",
  ".yml",
  ".yaml",
  ".ini",
  ".json",
  ".config",
  ".pem",
  ".crt",
  ".key",
  ".cert",

  // System / Hidden
  ".DS_Store",
  ".Thumbs.db",
  ".desktop",
  ".lnk",
]);

export function isAllowedFile(fileName: string): boolean {
  const ext = fileName.slice(fileName.lastIndexOf(".")).toLowerCase();
  return !prohibitedExtensions.has(ext);
}

export function formatBytes(bytes: number) {
  const units = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  if (bytes === 0) return '0 Bytes';

  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  const value = bytes / Math.pow(1024, i);

  return `${value.toFixed(2)} ${units[i]}`;
}

export function truncateFileName(fileName: string, maxLength = 18) {
  const dotIndex = fileName.lastIndexOf('.');
  const name = dotIndex !== -1 ? fileName.slice(0, dotIndex) : fileName;
  const ext = dotIndex !== -1 ? fileName.slice(dotIndex) : '';

  if (name.length > maxLength) {
    return name.slice(0, maxLength) + '...' + ext;
  }

  return fileName;
}

export function shouldInclude(filename: string) {
  const isHlsM3U8 = /-hls\.m3u8$/i.test(filename);
  const isExcluded = /\.m3u8$/i.test(filename) || /\.ts$/i.test(filename);
  return isHlsM3U8 || !isExcluded;
}

// Build API base URL from the app's current domain
export function getApiBaseUrl() {
  if (process.env.REACT_APP_LEGAL_API_HOST) {
    return process.env.REACT_APP_LEGAL_API_HOST;
  }

  const { protocol, hostname } = window.location;

  // Split hostname into parts
  const parts = hostname.split(".");

  // If the domain has "dev" before the root (example: app.dev.example.com)
  // then we want to keep "dev.example.com"
  let baseDomain: string;

  if (parts.length >= 3 && parts[parts.length - 3] === "dev") {
    // e.g., app.dev.example.com -> dev.example.com
    baseDomain = parts.slice(-3).join(".");
  } else {
    // e.g., app.example.com -> example.com
    baseDomain = parts.slice(-2).join(".");
  }

  return `${protocol}//t2gp-social-legal-api.${baseDomain}`;
}