import { But<PERSON> } from "primereact/button";
import {
  Column,
  ColumnBodyOptions,
  ColumnFilterElementTemplateOptions,
} from "primereact/column";
import { DataTable, DataTableFilterMeta, DataTableSelectEvent } from "primereact/datatable";
import React from "react";
import EvidenceFileDialog from "./evidenceFileDialog";
import { Content } from "./dataModel";
import { getFileType } from "./utility";
import { Dropdown, DropdownChangeEvent } from "primereact/dropdown";
import { FilterMatchMode } from "primereact/api";
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import { InputText } from 'primereact/inputtext';
import { deleteFile } from "./api";
import { DataTablePtOptions, EvidenceTableStyle } from "./style";
import { ContextMenu } from "primereact/contextmenu";
import { MenuItem, MenuItemCommandEvent } from "primereact/menuitem";
import { confirmDialog, ConfirmDialog } from "primereact/confirmdialog";

const fileTypes = ["Text", "Stream", "Image"];

interface EvidenceTableProps {
  issueId: string | undefined;
  evidenceList: Content[];
  isLoading: boolean;
  onFileDeleted: (fileName: string) => void;
  onFileDeleteError: (fileName: string) => void;
}

const EvidenceTable = (props: EvidenceTableProps) => {
  const [isViewEvidenceModalOpen, setIsViewEvidenceModalOpen] = React.useState(false);
  const [filters, setFilters] = React.useState<DataTableFilterMeta>({
    fileName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    fileType: { value: null, matchMode: FilterMatchMode.EQUALS },
  })

  const contextMenuRef = React.useRef<ContextMenu | null>(null);
  const selectedContentRef = React.useRef<any>({
    fileName: "",
    url: "",
    signedUrl: "",
  });

  const onClickView = React.useCallback(
    (fileName: string, url: string, signedUrl: string) => {
      selectedContentRef.current = {
        fileName: fileName,
        url: url,
        signedUrl: signedUrl
      };

      setIsViewEvidenceModalOpen(true);
    },
    []
  );

  const onClickViewContextMenu = React.useCallback((event: MenuItemCommandEvent) => {
    onClickView(selectedContentRef.current.fileName, selectedContentRef.current.url, selectedContentRef.current.signedUrl);
  }, [onClickView]);

  const onClickDelete = React.useCallback(
    (fileName: string) => {
      deleteFile(props.issueId ?? "", fileName)
        .then(() => props.onFileDeleted(fileName))
        .catch(error => props.onFileDeleteError(fileName));
    },
    [props]
  );

  const confirm = React.useCallback((fileName: string) => {
    confirmDialog({
      message: 'Are you sure you want to proceed?',
      header: 'Confirmation',
      icon: 'pi pi-exclamation-triangle',
      defaultFocus: 'accept',
      accept: () => onClickDelete(fileName),
      reject: () => { }
    });
  }, [onClickDelete]);

  const onClickDeleteContextMenu = React.useCallback((event: MenuItemCommandEvent) => {
    confirm(selectedContentRef.current.fileName);
  }, [confirm]);

  const items: MenuItem[] = [
    { label: 'View', icon: 'pi pi-search', command: onClickViewContextMenu },
    { label: 'Delete', icon: 'pi pi-times', command: onClickDeleteContextMenu }
  ];

  const fileAction = React.useCallback(
    (data: Content, options: ColumnBodyOptions) => {
      let downloadButton = <></>;
      if (getFileType(data.fileName) === "Text") {
        downloadButton = (
          <a href={data.signedUrl}>
            <Button severity="help" label="Download" />
          </a>
        );
      }

      return (
        <div className="flex gap-2">
          <Button
            severity="info"
            onClick={() => onClickView(data.fileName, data.url, data.signedUrl)}
            label="View"
          />
          {downloadButton}
        </div>
      );
    },
    [onClickView]
  );

  const fileTypeRowFilterTemplate = React.useCallback(
    (options: ColumnFilterElementTemplateOptions) => (
      <Dropdown
        value={options.value}
        options={fileTypes}
        onChange={(e: DropdownChangeEvent) => {
          options.filterApplyCallback(e.value);
        }}
        className="p-column-filter"
        showClear
        style={{ minWidth: "12rem" }}
      />
    ),
    []
  );

  const onFileNameFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let filter = { ...filters };

    // @ts-ignore
    filter['fileName'].value = e.target.value
    setFilters(filter);
  };

  const renderHeader = () => (
    <div className="flex justify-content-end">
      <IconField iconPosition="left" style={{ borderRadius: '8px' }}>
        <InputIcon className="pi pi-search" />
        <InputText
          placeholder="File Name Search"
          //@ts-ignore
          value={filters['fileName'].value}
          onChange={onFileNameFilterChange}
        />
      </IconField>
    </div>
  );

  return (
    <>
      <EvidenceFileDialog
        visible={isViewEvidenceModalOpen}
        onHide={() => setIsViewEvidenceModalOpen(false)}
        issueId={props.issueId ?? ""}
        content={selectedContentRef.current}
      />
      <ContextMenu model={items} ref={contextMenuRef} breakpoint="767px" />
      <ConfirmDialog />
      <DataTable
        emptyMessage="No evidence files found"
        header={renderHeader()}
        style={EvidenceTableStyle}
        value={props.evidenceList}
        filters={filters}
        paginator
        rows={10}
        rowsPerPageOptions={[10, 25]}
        loading={props.isLoading}
        loadingIcon="pi pi-spinner-dotted"
        onContextMenuSelectionChange={(e) => {
          selectedContentRef.current = e.value;
          contextMenuRef.current?.show(e.originalEvent);
        }}
        pt={DataTablePtOptions}
      >
        <Column field="fileName" header="File Name" sortable />
        <Column field="uploader" header="Uploaded By" />
        <Column field="lastModified" header="Last Modified" sortable />
        <Column
          header="File Type"
          field="fileType"
          filter
          filterElement={fileTypeRowFilterTemplate}
          filterMenuStyle={{ width: "14rem" }}
          showFilterMatchModes={false}
          sortable
        />
        <Column body={fileAction} header="Action" />
      </DataTable>
    </>
  );
};

export default EvidenceTable;
