import { But<PERSON> } from "primereact/button";
import {
  Column,
  ColumnBodyOptions,
  ColumnFilterElementTemplateOptions,
} from "primereact/column";
import { DataTable, DataTableFilterMeta } from "primereact/datatable";
import React from "react";
import EvidenceFileDialog from "./evidenceFileDialog";
import { Content } from "./dataModel";
import { getFileType } from "./utility";
import { Dropdown, DropdownChangeEvent } from "primereact/dropdown";
import { FilterMatchMode } from "primereact/api";
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import { InputText } from 'primereact/inputtext';
import { deleteFile } from "./api";

const fileTypes = ["Text", "Stream", "Image"];

interface EvidenceTableProps {
  issueId: string | undefined;
  evidenceList: Content[];
  isLoading: boolean;
  onFileDeleted: (fileName: string) => void;
  onFileDeleteError: (fileName: string) => void;
}

const EvidenceTable = (props: EvidenceTableProps) => {
  const [isViewEvidenceModalOpen, setIsViewEvidenceModalOpen] = React.useState(false);
  const [filters, setFilters] = React.useState<DataTableFilterMeta>({ 
    fileName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    fileType: { value: null, matchMode: FilterMatchMode.EQUALS },
  })

  const selectedContentRef = React.useRef<any>({
    fileName: "",
    url: "",
    signedUrl: "",
  });

  const onClickView = React.useCallback(
    (fileName: string, url: string, signedUrl: string) => {
      selectedContentRef.current = {
        fileName: fileName,
        url: url,
        signedUrl: signedUrl
      };

      setIsViewEvidenceModalOpen(true);
    },
    []
  );

  const onClickDelete = React.useCallback(
    (fileName: string) => {
      deleteFile(props.issueId ?? "", fileName)
      .then(() => props.onFileDeleted(fileName))
      .catch(error => props.onFileDeleteError(fileName));
    },
    [props]
  );

  const fileAction = React.useCallback(
    (data: Content, options: ColumnBodyOptions) => {
      let downloadButton = <></>;
      if (getFileType(data.fileName) === "Text") {
        downloadButton = (
          <a href={data.signedUrl}>
            <Button severity="help" label="Download" />
          </a>
        );
      }
      return (
        <div className="flex gap-2">
          <Button
            onClick={() => onClickView(data.fileName, data.url, data.signedUrl)}
            label="View"
          />
          {downloadButton}
          <Button label="Delete" onClick={() => onClickDelete(data.fileName)} />
        </div>
      );
    },
    [onClickView, onClickDelete]
  );

  const fileTypeRowFilterTemplate = React.useCallback(
    (options: ColumnFilterElementTemplateOptions) => (
      <Dropdown
        value={options.value}
        options={fileTypes}
        onChange={(e: DropdownChangeEvent) => {
          options.filterApplyCallback(e.value);
        }}
        className="p-column-filter"
        showClear
        style={{ minWidth: "12rem" }}
      />
    ),
    []
  );

  const onFileNameFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let filter = { ...filters };

    // @ts-ignore
    filter['fileName'].value = e.target.value
    setFilters(filter);
  };

  const renderHeader = () => (
    <div className="flex justify-content-end">
      <IconField iconPosition="left">
        <InputIcon className="pi pi-search" />
        <InputText
          placeholder="File Name Search"
          //@ts-ignore
          value={filters['fileName'].value} 
          onChange={onFileNameFilterChange} 
        />
      </IconField>
    </div>
  );

  return (
    <>
      <EvidenceFileDialog
        visible={isViewEvidenceModalOpen}
        onHide={() => setIsViewEvidenceModalOpen(false)}
        issueId={props.issueId ?? ""}
        content={selectedContentRef.current}
      />
      <DataTable
        header={renderHeader()}
        style={{
          minWidth: "1200px",
          justifyContent: "center",
        }}
        value={props.evidenceList}
        filters={filters}
        paginator
        rows={10}
        rowsPerPageOptions={[10, 25]}
        loading={props.isLoading}
        loadingIcon="pi pi-spinner-dotted"
      >
        <Column field="fileName" filter header="File Name" />
        <Column field="uploader" header="Uploaded By" />
        <Column field="lastModified" header="Last Modified" sortable />
        <Column
          header="File Type"
          field="fileType"
          filter
          filterElement={fileTypeRowFilterTemplate}
          filterMenuStyle={{ width: "14rem" }}
          showFilterMatchModes={false}
          sortable
        />
        <Column body={fileAction} header="Action" />
      </DataTable>
    </>
  );
};

export default EvidenceTable;
