import { useParams } from "react-router-dom";
import EvidenceTable from "./evidenceTable";
import EvidenceFileUpload from "./evidenceFileUpload";
import React from "react";
import { getAuditLogs, getEvidenceUrls, login } from "./api";
import { AuditLog, Content } from "./dataModel";
import { formatTime, getApiBaseUrl, getFileType, shouldInclude } from "./utility";
import { Toast } from "primereact/toast";
import { Dialog } from "primereact/dialog";
import { usePresignedUrls } from "./filePresignContext";
import AuditLogTable from "./auditLogTable";
import { useAuth } from "./authContext";
import { TabPanel, TabView } from "primereact/tabview";
import { TabPanelPtOptions, TabPanelStyle, TabViewPtOptions } from "./style";

function shouldHide(fileName: string): boolean {
  return fileName.endsWith('.ts') || getFileType(fileName) === "Video";
}

const EvidenceHome = () => {
  const { issueId } = useParams();
  const { setPresignedUrl } = usePresignedUrls();

  const [evidenceList, setEvidenceList] = React.useState<Content[]>([]);
  const [auditLogList, setAuditLogList] = React.useState<AuditLog[]>([]);
  const [isEvidenceTableLoading, setIsEvidenceTableLoading] = React.useState(false);
  const [isAuditLogTableLoading, setIsAuditLogTableLoading] = React.useState(false);
  const [errorText, setErrorText] = React.useState("");

  const toastRef = React.useRef<Toast>(null);
  const auth = useAuth();

  const loadFiles = () => {
    if (!issueId) {
      return;
    }

    setIsEvidenceTableLoading(true);
    getEvidenceUrls(issueId)
      .then((items) => {
        auth.setIsLoggedIn(true);
        if (!items) {
          setEvidenceList([]);
          setIsEvidenceTableLoading(false);
          return;
        }

        const contentList: Content[] = [];
        items.forEach((item: any) => {
          const fileName = item.key.split("/").pop();
          if (shouldInclude(fileName)) {
            contentList.push({
              fileName: item.key.split("/").pop(),
              lastModified: formatTime(item.lastModified),
              url: item.url,
              signedUrl: item.presignedUrl,
              fileType: getFileType(item.key),
              uploader: item.uploader,
            });
          }
          setPresignedUrl(fileName, item.presignedUrl);
        });
        setEvidenceList(contentList);
        setIsEvidenceTableLoading(false);
      })
      .catch((error) => {
        if (error.message === 'Unauthorized') {
          login(issueId);
        }
        console.error("Failed to getEvidenceUrls", error);
        setIsEvidenceTableLoading(false);
      });
  };

  const loadAuditLogs = () => {
    if (!issueId) {
      return;
    }

    setIsAuditLogTableLoading(true);
    getAuditLogs(issueId)
      .then((items) => {
        setAuditLogList([...items]);
        setIsAuditLogTableLoading(false);
      })
      .catch((error) => {
        if (error.message === 'Unauthorized') {
          login(issueId);
        }
        console.error("Failed to getAuditLogs", error);
        setIsAuditLogTableLoading(false);
      });
  };

  const updateView = (fileName: string) => {
    loadFiles();
    loadAuditLogs();
  };

  const onFileDeleted = (fileName: string) => {
    toastRef.current?.show({
      severity: "success",
      summary: "Success",
      detail: `${fileName} has been successfully deleted.`,
      life: 3000,
    });
    updateView(fileName);
  };

  const onFileUploaded = (fileName: string) => {
    toastRef.current?.show({
      severity: "success",
      summary: "Success",
      detail: `Uploaded ${fileName}`,
      life: 2000,
    });

    if (getFileType(fileName) === "Video") {
      toastRef.current?.show({
        severity: "info",
        detail: `Waiting for ${fileName} to be converted.`,
        life: 2000,
      });
    }

    updateView(fileName);
  };

  const onFileUploadError = (fileName: string) => {
    toastRef.current?.show({
      severity: "error",
      summary: "Error",
      detail: `Failed to delete ${fileName}`,
      life: 3000,
    });
    setErrorText(`Failed to upload ${fileName}`);
  };

  const onMessage = (event: any) => {
    const data = JSON.parse(event.data);
    console.log("Received message:", data);
    if (data.eventName === 'completed') {
      toastRef.current?.show({
        severity: "success",
        summary: "success",
        detail: "Video conversion completed.",
        life: 3000,
      });
      loadFiles();
    } else if (data.eventName === 'success') {
      toastRef.current?.show({
        severity: "info",
        summary: "Info",
        detail: "Converting video, please wait...",
        life: 3000,
      });
    }
  };

  const onError = (err: any) => {
    console.error("SSE error:", err);
  };

  React.useEffect(() => {
    loadFiles();
    loadAuditLogs();

    const eventSource = new EventSource(`${getApiBaseUrl()}/${issueId}/events`);
    eventSource.onmessage = onMessage;
    eventSource.onerror = onError;

    return () => eventSource.close();
  }, []);

  const onDisallowedFileSelect = React.useCallback(
    (fileName: string) => setErrorText(`${fileName} is not allowed to be uploaded.`), []);

  return (
    <div>
      <Toast ref={toastRef} />
      <Dialog
        header="Error"
        visible={errorText !== ""}
        style={{ width: "20vw" }}
        onHide={() => setErrorText("")}
        draggable={false}
        resizable={false}
      >
        <p className="m-0">{errorText}</p>
      </Dialog>
      <h2>{`Evidence Files For ${issueId}`}</h2>
      <TabView pt={TabViewPtOptions}>
        <TabPanel header="Files" pt={TabPanelPtOptions}>
          <div className="flex flex-row gap-2" style={TabPanelStyle}>
            <EvidenceTable
              issueId={issueId}
              evidenceList={evidenceList.filter(item => !shouldHide(item.fileName))}
              isLoading={isEvidenceTableLoading}
              onFileDeleted={onFileDeleted}
              onFileDeleteError={(fileName: string) =>
                setErrorText(`Failed to delete ${fileName}`)
              }
            />
            <EvidenceFileUpload
              issueId={issueId}
              onUploadCompleted={onFileUploaded}
              onUploadError={onFileUploadError}
              onDisallowedFileSelect={onDisallowedFileSelect}
            />
          </div>
        </TabPanel>
        <TabPanel header="Audit Log" pt={TabPanelPtOptions}>
          <AuditLogTable
            issueId={issueId}
            auditLogList={auditLogList}
            isLoading={isAuditLogTableLoading}
          />
        </TabPanel>
      </TabView>
    </div>
  );
};

export default EvidenceHome;
