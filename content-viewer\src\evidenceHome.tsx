import { useParams } from "react-router-dom";
import EvidenceTable from "./evidenceTable";
import { Button } from "primereact/button";
import EvidenceFileUpload from "./evidenceFileUpload";
import React from "react";
import { getAuditLogs, getEvidenceUrls, login, logout } from "./api";
import { AuditLog, Content } from "./dataModel";
import { formatTime, getFileType } from "./utility";
import { Toast } from "primereact/toast";
import { Dialog } from "primereact/dialog";
import { usePresignedUrls } from "./filePresignContext";
import AuditLogTable from "./auditLogTable";

const EvidenceHome = () => {
  const { issueId } = useParams();
  const { setPresignedUrl } = usePresignedUrls();

  const [evidenceList, setEvidenceList] = React.useState<Content[]>([]);
  const [auditLogList, setAuditLogList] = React.useState<AuditLog[]>([]);
  const [isEvidenceTableLoading, setIsEvidenceTableLoading] = React.useState(false);
  const [isAuditLogTableLoading, setIsAuditLogTableLoading] = React.useState(false);
  const [errorText, setErrorText] = React.useState("");

  const toastRef = React.useRef<Toast>(null);

  const loadFiles = () => {
    if (!issueId) {
      return;
    }

    setIsEvidenceTableLoading(true);
    getEvidenceUrls(issueId)
      .then((items) => {
        const contentList: Content[] = [];
        items.forEach((item: any) => {
          const fileName = item.key.split("/").pop();
          if (!fileName.endsWith('.ts')) {
            contentList.push({
              fileName: item.key.split("/").pop(),
              lastModified: formatTime(item.lastModified),
              url: item.url,
              signedUrl: item.presignedUrl,
              fileType: getFileType(item.key),
              uploader: item.uploader,
            });
          }
          setPresignedUrl(fileName, item.presignedUrl);
        });
        setEvidenceList(contentList);
        setIsEvidenceTableLoading(false);
      })
      .catch((error) => {
        if (error.message === 'Unauthorized') {
          login(issueId);
        }
        console.error("Failed to getEvidenceUrls", error);
        setIsEvidenceTableLoading(false);
      });
  };

  const loadAuditLogs = () => {
    if (!issueId) {
      return;
    }

    setIsAuditLogTableLoading(true);
    getAuditLogs(issueId)
      .then((items) => {
        setAuditLogList([...items]);
        setIsAuditLogTableLoading(false);
      })
      .catch((error) => {
        if (error.message === 'Unauthorized') {
          login(issueId);
        }
        console.error("Failed to getAuditLogs", error);
        setIsAuditLogTableLoading(false);
      });
  };

  const updateView = (fileName: string) => {
    loadFiles();
    loadAuditLogs();
  };

  React.useEffect(() => {
    loadFiles();
    loadAuditLogs();
  }, []);

  const onDisallowedFileSelect = React.useCallback(
    (fileName: string) => setErrorText(`${fileName} is not allowed to be uploaded.`), []);

  const logoutButton = () => {
    return <Button label="Log out" onClick={logout} />;
  };

  return (
    <div className="flex flex-column align-items-center gap-4">
      <Toast ref={toastRef} />
      <Dialog
        header="Error"
        visible={errorText !== ""}
        style={{ width: "20vw" }}
        onHide={() => setErrorText("")}
        draggable={false}
        resizable={false}
      >
        <p className="m-0">{errorText}</p>
      </Dialog>
      <h2>{`Evidence Files for ${issueId}`}</h2>
      {/* {logoutButton()} */}
      <div className="flex flex-row gap-2">
        <EvidenceTable
          issueId={issueId}
          evidenceList={evidenceList}
          isLoading={isEvidenceTableLoading}
          onFileDeleted={(fileName: string) => updateView(fileName)}
          onFileDeleteError={(fileName: string) =>
            setErrorText(`Failed to delete ${fileName}`)
          }
        />
        <EvidenceFileUpload
          issueId={issueId}
          onUploadCompleted={(fileName: string) => updateView(fileName)}
          onUploadError={(fileName: string) =>
            setErrorText(`Failed to upload ${fileName}`)
          }
          onDisallowedFileSelect={onDisallowedFileSelect}
        />
      </div>
      <AuditLogTable
        issueId={issueId}
        auditLogList={auditLogList}
        isLoading={isAuditLogTableLoading}
      />
    </div>
  );
};

export default EvidenceHome;
