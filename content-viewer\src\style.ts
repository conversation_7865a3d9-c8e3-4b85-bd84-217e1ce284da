import { DataTablePassThroughOptions } from 'primereact/datatable';
import { FileUploadPassThroughOptions } from 'primereact/fileupload';
import { TabPanelPassThroughOptions, TabViewPassThroughOptions } from 'primereact/tabview';
import React from 'react';

export const RootAppContainerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    gap: '36px',
    padding: '63px 144px 10px 144px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'start',
    background: '#E9E9E9'
}

export const BannerRootStyle: React.CSSProperties = {
    width: '100%',
    height: '80px',
    backgroundColor: '#C0C0C0',
    borderBottom: '1px solid #737373',
    padding: '16px 144px 16px 144px',
    display: 'flex',
    alignItems: 'space-between',
    justifyContent: 'space-between',
}

export const LogoutButtonStyle: React.CSSProperties = {
    width: '114px',
    height: '48px',
    borderRadius: '4px',
    padding: '12px 24px 12px 24px',
    border: '1px solid #000000',
    fontSize: '16px',
    fontWeight: 700,
    margin: '0',
    backgroundColor: '#C0C0C0',
    color: '#000000',
}

export const TabPanelStyle: React.CSSProperties = {
    width: '100%',
    padding: '0px',
    backgroundColor: '#E9E9E9',
}

export const EvidenceTableStyle: React.CSSProperties = {
    width: '100%',
    justifyContent: 'center',
}

export const HeaderTitleStyle: React.CSSProperties = {
    color: '#000000',
    fontSize: '18px',
    fontWeight: 700,
}

export const FileUploadPtOptions: FileUploadPassThroughOptions = {
    root: {
        className: 'flex flex-column',
        style: {
            marginTop: '36px',
            minWidth: "395px",
            maxWidth: "100%",
        }
    },
    chooseButton: {
        style: {
            width: '347px',
            height: '48px',
            radius: '44px',
            border: '1px solid #0063B1',
        }
    },
    uploadButton: {
        root: {
            style: {
                width: '347px',
                height: '48px',
                radius: '44px',
                border: '1px solid #386E09',
            }
        },
    },
    cancelButton: {
        root: {
            style: {
                width: '347px',
                height: '48px',
                radius: '44px',
                border: '1px solid #BD2222',
            }
        },
    },
    buttonbar: {
        className: 'flex flex-column',
        style: { 
            backgroundColor: '#DDDDDD',
            minHeight: "208px",
            gap: '8px',
            justifyContent: 'center',

        }
    },
    content: {
        style: {
            minHeight: "160px",
            padding: '0px',
            alignContent: 'center',
        }
    },
}

export const TabViewPtOptions: TabViewPassThroughOptions = {
    panelContainer: {
        style: TabPanelStyle,
    },
}

export const TabPanelPtOptions: TabPanelPassThroughOptions = {
    headerTitle: {
        style: {
            color: '#000000',
        }
    },
}

export const TabTitleStyle: React.CSSProperties = {
    color: '#000000',
    fontSize: '16px',
    fontWeight: 700,
}

export const DataTablePtOptions: DataTablePassThroughOptions = {
    root: {
        style: {
            marginTop: '36px',
        }
    },
    header: {
        style: {
            backgroundColor: '#DDDDDD',
            borderBottom: '1px solid #AEAEAE',
        }
    },
    footer: {
        style: {
            backgroundColor: '#DDDDDD',
        }
    },
    paginator: {
        root: {
            style: {
                backgroundColor: '#E9E9E9',
            }
        },
        pageButton: {
            style: {
                backgroundColor: '#D3FFFF',
                borderRadius: '48px',
            }
        },
        RPPDropdown: {
            root: {
                style: {
                    backgroundColor: '#E9E9E9',
                }
            },
        }
    },
    column: {
        headerCell: {
            style: {
                backgroundColor: '#DDDDDD',
            }
        },
        headerTitle: {
            style: HeaderTitleStyle
        },
    },
}
