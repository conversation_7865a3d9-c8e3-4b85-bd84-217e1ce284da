import React, { useEffect, useState } from "react";
import ForgeRecon<PERSON><PERSON>, {
  Checkbox,
  Text,
  Textfield,
  Label,
  Select,
  Stack,
  Tabs,
  TabList,
  Tab,
  TabPanel,
} from "@forge/react";
import { invoke, requestJira, view } from "@forge/bridge";
import { COUNTRY_CODES, OfficerFields } from "./officer";

const App = () => {
  const [agencyName, setAgencyName] = useState("");
  const [caseNumber, setCaseNumber] = useState("");
  const [reportedToLE, setReportedToLE] = useState(false);
  const [servedLegalDomestic, setServedLegalDomestic] = useState(false);
  const [servedLegalIntl, setServedLegalIntl] = useState(false);
  const [fleaCountry, setFleaCountry] = useState("");
  const [isDataReady, setIsDataReady] = useState(false);

  const productContextRef = React.useRef(null);

  const onChangeAgencyName = (e) => {
    setAgencyName(e.target.value);
  };

  const onBlurAgencyName = (e) => {
    invoke("setAgencyName", {
      context: productContextRef.current,
      agencyName: agencyName,
    });
  };

  const onChangeCaseNumber = (e) => {
    setCaseNumber(e.target.value);
  };

  const onBlurCaseNumber = (e) => {
    invoke("setCaseNumber", {
      context: productContextRef.current,
      caseNumber: caseNumber,
    });
  };

  const onChangeReportedToLE = (e) => {
    setReportedToLE(e.target.checked);
    invoke('setReportedToLE', { context: productContextRef.current, reportedToLE: e.target.checked });
  };
  
  const onChangeServedLegalDomestic = (e) => {
    setServedLegalDomestic(e.target.checked);
    invoke('setServedLegalDomestic', { context: productContextRef.current, servedLegalDomestic: e.target.checked });
  };
  
  const onChangeServedLegalIntl = (e) => {
    setServedLegalIntl(e.target.checked);
    invoke('setServedLegalIntl', { context: productContextRef.current, servedLegalIntl: e.target.checked });
  };

  const onChangeFleaCountry = (e) => {
    setFleaCountry(e);
    invoke('setFleaCountry', { context: productContextRef.current, fleaCountry: e });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const context = await view.getContext();
        if (context.accountId) {
          const res = await requestJira(
            `/rest/api/3/user?accountId=${context.accountId}`
          );
          const userData = await res.json();
          productContextRef.current = { user: userData.displayName };
        }

        productContextRef.current = {
          ...productContextRef.current,
          cloudId: context.cloudId,
          issueId: context.extension.issue.key
        };

        const [
          agencyName,
          caseNumber,
          reportedToLE,
          servedLegalDomestic,
          servedLegalIntl,
          fleaCountry,
        ] = await Promise.all([
          invoke("getAgencyName", { issueId: productContextRef.current.issueId }),
          invoke("getCaseNumber", { issueId: productContextRef.current.issueId }),
          invoke("getReportedToLE", { issueId: productContextRef.current.issueId }),
          invoke("getServedLegalDomestic", { issueId: productContextRef.current.issueId }),
          invoke("getServedLegalIntl", { issueId: productContextRef.current.issueId }),
          invoke("getFleaCountry", { issueId: productContextRef.current.issueId }),
        ]);

        setAgencyName(agencyName);
        setCaseNumber(caseNumber);
        setReportedToLE(reportedToLE);
        setServedLegalDomestic(servedLegalDomestic);
        setServedLegalIntl(servedLegalIntl);
        setFleaCountry(JSON.parse(fleaCountry));

        setIsDataReady(true);
      } catch (error) {
        console.error("Failed to fetch issue data:", error);
      } finally {
        setIsDataReady(true);
      }
    };

    fetchData();
  }, []);

  const tabs = () => {
    return (
      <Tabs id="reportedPerson-tabs">
        <TabList>
          <Tab>Law Enforcement Information</Tab>
          <Tab>Officer Information</Tab>
        </TabList>
        <TabPanel>
          <Stack alignInline="stretch" grow="fill">
            <Label labelFor="agencyName">Agency Name</Label>
            <Textfield
              id="agencyName"
              value={agencyName}
              onChange={onChangeAgencyName}
              onBlur={onBlurAgencyName}
              maxLength={255}
            />

            <Label labelFor="caseNumber">Case Number</Label>
            <Textfield
              id="caseNumber"
              value={caseNumber}
              onChange={onChangeCaseNumber}
              onBlur={onBlurCaseNumber}
              maxLength={100}
            />

            <Label labelFor="reportedToLE">Reported to Law Enforcement?</Label>
            <Checkbox
              id="reportedToLE"
              isChecked={reportedToLE}
              onChange={onChangeReportedToLE}
            />

            <Label labelFor="servedLegalDomestic">
              Served Legal Process (Domestic)?
            </Label>
            <Checkbox
              id="servedLegalDomestic"
              isChecked={servedLegalDomestic}
              onChange={onChangeServedLegalDomestic}
            />

            <Label labelFor="servedLegalIntl">
              Served Legal Process (International)?
            </Label>
            <Checkbox
              id="servedLegalIntl"
              isChecked={servedLegalIntl}
              onChange={onChangeServedLegalIntl}
            />

            <Label labelFor="fleaCountry">Flea Country</Label>
            <Select
              id="fleaCountry"
              options={COUNTRY_CODES}
              value={fleaCountry}
              onChange={onChangeFleaCountry}
            />
          </Stack>
        </TabPanel>
        <TabPanel>
          <Stack alignInline="stretch" grow="fill">
            <OfficerFields context={productContextRef.current} />
          </Stack>
        </TabPanel>
      </Tabs>
    );
  };

  return <>{!isDataReady ? <Text>Loading data…</Text> : tabs()}</>;
};

ForgeReconciler.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
