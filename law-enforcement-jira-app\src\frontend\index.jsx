import React, { useEffect, useState } from 'react';
import ForgeReconciler, { useProductContext, Checkbox, Text, Textfield, Label, Select, Stack, Tabs, TabList, Tab, TabPanel, } from '@forge/react';
import { invoke } from '@forge/bridge';
import { COUNTRY_CODES, OfficerFields } from './officer';

const App = () => {
  const [agencyName, setAgencyName] = useState('');
  const [caseNumber, setCaseNumber] = useState('');
  const [reportedToLE, setReportedToLE] = useState(false);
  const [servedLegalDomestic, setServedLegalDomestic] = useState(false);
  const [servedLegalIntl, setServedLegalIntl] = useState(false);
  const [fleaCountry, setFleaCountry] = useState('');
  const [isDataReady, setIsDataReady] = useState(false);

  const context = useProductContext();

  const onChangeAgencyName = (e) => {
    setAgencyName(e.target.value);
  };

  const onBlurAgencyName = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setAgencyName', { cloudId: context.cloudId, issueId: key, agencyName: agencyName });
  };

  const onChangeCaseNumber = (e) => {
    setCaseNumber(e.target.value);
  };

  const onBliurCaseNumber = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setCaseNumber', { cloudId: context.cloudId, issueId: key, caseNumber: caseNumber });
  };

  const onChangeReportedToLE = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    setReportedToLE(e.target.checked);
    invoke('setReportedToLE', { cloudId: context.cloudId, issueId: key, reportedToLE: e.target.checked });
  };

  const onChangeServedLegalDomestic = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    setServedLegalDomestic(e.target.checked);
    invoke('setServedLegalDomestic', { cloudId: context.cloudId, issueId: key, servedLegalDomestic: e.target.checked });
  };

  const onChangeServedLegalIntl = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    setServedLegalIntl(e.target.checked);
    invoke('setServedLegalIntl', { cloudId: context.cloudId, issueId: key, servedLegalIntl: e.target.checked });
  };

  const onChangeFleaCountry = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    setFleaCountry(e);
    invoke('setFleaCountry', { cloudId: context.cloudId, issueId: key, fleaCountry: e });
  };

  useEffect(() => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;

    const fetchData = async () => {
      try {
        const { key } = context.extension.issue;

        const [
          agencyName,
          caseNumber,
          reportedToLE,
          servedLegalDomestic,
          servedLegalIntl,
          fleaCountry,
        ] = await Promise.all([
          invoke('getAgencyName', { issueId: key }),
          invoke('getCaseNumber', { issueId: key }),
          invoke('getReportedToLE', { issueId: key }),
          invoke('getServedLegalDomestic', { issueId: key }),
          invoke('getServedLegalIntl', { issueId: key }),
          invoke('getFleaCountry', { issueId: key }),
        ]);

        setAgencyName(agencyName);
        setCaseNumber(caseNumber);
        setReportedToLE(reportedToLE);
        setServedLegalDomestic(servedLegalDomestic);
        setServedLegalIntl(servedLegalIntl);
        setFleaCountry(JSON.parse(fleaCountry));

        setIsDataReady(true);
      } catch (error) {
        console.error('Failed to fetch issue data:', error);
        setIsDataReady(true);
      }
    };

    fetchData();
  }, [context]);

  const tabs = () => {
    return (
      <Tabs id="reportedPerson-tabs">
        <TabList>
          <Tab>Law Enforcement Information</Tab>
          <Tab>Officer Information</Tab>
        </TabList>
        <TabPanel>
          <Stack alignInline='stretch' grow='fill'>
            <Label labelFor="agencyName">Agency Name</Label>
            <Textfield id="agencyName" value={agencyName} onChange={onChangeAgencyName} onBlur={onBlurAgencyName} maxLength={255} />

            <Label labelFor="caseNumber">Case Number</Label>
            <Textfield id="caseNumber" value={caseNumber} onChange={onChangeCaseNumber} onBlur={onBliurCaseNumber} maxLength={100} />

            <Label labelFor="reportedToLE">Reported to Law Enforcement?</Label>
            <Checkbox id="reportedToLE" isChecked={reportedToLE} onChange={onChangeReportedToLE} />

            <Label labelFor="servedLegalDomestic">Served Legal Process (Domestic)?</Label>
            <Checkbox id="servedLegalDomestic" isChecked={servedLegalDomestic} onChange={onChangeServedLegalDomestic} />

            <Label labelFor="servedLegalIntl">Served Legal Process (International)?</Label>
            <Checkbox id="servedLegalIntl" isChecked={servedLegalIntl} onChange={onChangeServedLegalIntl} />

            <Label labelFor="fleaCountry">Flea Country</Label>
            <Select id="fleaCountry" options={COUNTRY_CODES} value={fleaCountry} onChange={onChangeFleaCountry} />
          </Stack>
        </TabPanel>
        <TabPanel>
          <Stack alignInline='stretch' grow='fill'>
            <OfficerFields context={context} />
          </Stack>
        </TabPanel>
      </Tabs>
    )
  }

  return (
    <>
      {!isDataReady ? <Text>Loading data…</Text> : tabs()}
    </>
  );
};

ForgeReconciler.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);