import React, { useEffect, useState } from 'react';
import ForgeReconciler, { useProductContext, Text, Textfield, Label, Select } from '@forge/react';
import { invoke } from '@forge/bridge';

const incidentTypeOptions = [
  { label: 'Child Pornography (possession, manufacture, and distribution)', value: 'Child Pornography (possession, manufacture, and distribution)' },
  { label: 'Child Sex Trafficking', value: 'Child Sex Trafficking' },
  { label: 'Child Sex Tourism', value: 'Child Sex Tourism' },
  { label: 'Child Sexual Molestation', value: 'Child Sexual Molestation' },
  { label: 'Misleading Domain Name', value: 'Misleading Domain Name' },
  { label: 'Misleading Words or Digital Images on the Internet', value: 'misleading_words_images' },
  { label: 'Online Enticement of Children for Sexual Acts', value: 'Online Enticement of Children for Sexual Acts' },
  { label: 'Unsolicited Obscene Material Sent to a Child', value: 'Unsolicited Obscene Material Sent to a Child' }
];

const reportAnnotationOptions = [
  { label: 'Sextortion', value: 'sextortion' },
  { label: 'Solicitation of child sexual assault material', value: 'csamSolicitation' },
  { label: 'Minor to minor interaction', value: 'minorToMinorInteraction' },
  { label: 'Spam', value: 'spam' },
  { label: 'Sadistic Online Exploitation', value: 'sadisticOnlineExploitation' }
]

const App = () => {
  const [incidentType, setIncidentType] = useState(null)
  const [platform, setPlatform] = useState('')
  const [escalateToHighPriority, setEscalateToHighPriority] = useState('')
  const [incidentTime, setIncidentTime] = useState('')
  const [incidentDateDescription, setIncidentDateDescription] = useState('')
  const [reportAnnotation, setReportAnnotation] = useState('')
  const [isDataReady, setIsDataReady] = useState(false);

  const context = useProductContext();

  const onChangeIncidentType = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    setIncidentType(e);
    invoke('setIncidentType', { cloudId: context.cloudId, issueId: key, type: e });
  }

  const onChangePlatoform = (e) => {
    setPlatform(e.target.value);
  }

  const onBlurPlatoform = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setPlatform', { cloudId: context.cloudId, issueId: key, platform: e.target.value });
  }

  const onChangeEscalateToHighPriority = (e) => {
    setEscalateToHighPriority(e.target.value);
  }

  const onBlurEscalateToHighPriority = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setEscalateToHighPriority', { cloudId: context.cloudId, issueId: key, priority: e.target.value });
  }

  const onChangeIncidentDate = (e) => {
    setIncidentTime(e.target.value);
  }

  const onBlurIncidentDate = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setIncidentDate', { cloudId: context.cloudId, issueId: key, date: e.target.value });
  }

  const onChangeIncidentDateDescription = (e) => {
    setIncidentDateDescription(e.target.value);
  }

  const onBlurIncidentDateDescription = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    invoke('setIncidentDateDescription', { cloudId: context.cloudId, issueId: key, description: e.target.value });
  }

  const onChangeReportAnnotation = (e) => {
    if (context == undefined) return;
    const { key } = context.extension.issue;
    setReportAnnotation(e);
    invoke('setReportAnnotation', { cloudId: context.cloudId, issueId: key, annotations: e });
  }

  useEffect(() => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;

    const fetchData = async () => {
      try {
        const { key } = context.extension.issue;

        const [
          platform,
          type,
          priority,
          date,
          description,
          annotations
        ] = await Promise.all([
          invoke('getPlatform', { issueId: key }),
          invoke('getIncidentType', { issueId: key }),
          invoke('getEscalateToHighPriority', { issueId: key }),
          invoke('getIncidentDate', { issueId: key }),
          invoke('getIncidentDateDescription', { issueId: key }),
          invoke('getReportAnnotation', { issueId: key })
        ]);

        setPlatform(platform);
        setIncidentType(JSON.parse(type));
        setEscalateToHighPriority(priority);
        setIncidentTime(date);
        setIncidentDateDescription(description);
        setReportAnnotation(JSON.parse(annotations));

        setIsDataReady(true);
      } catch (error) {
        console.error('Failed to fetch issue data:', error);
        setIsDataReady(true);
      }
    };

    fetchData();
  }, [context]);

  return (
    <>
      {!isDataReady ? (
        <Text>Loading data…</Text>
      ) : (
        <>
          <Label labelFor="incidentType">Incident Type</Label>
          <Select id="incidentType" value={incidentType} options={incidentTypeOptions} onChange={onChangeIncidentType} />
          <Label labelFor="platform">Platform</Label>
          <Textfield id="platform" value={platform} onChange={onChangePlatoform} onBlur={onBlurPlatoform} maxLength={255} />
          <Label labelFor="escalateToHighPriority">Escalate to High Priority</Label>
          <Textfield id="escalateToHighPriority" value={escalateToHighPriority} onChange={onChangeEscalateToHighPriority} onBlur={onBlurEscalateToHighPriority} maxLength={3000} />
          <Label labelFor="incident-datetime">Incident Datetime</Label>
          <Textfield type='datetime-local' id="incident-datetime" value={incidentTime} onChange={onChangeIncidentDate} onBlur={onBlurIncidentDate} />
          <Label labelFor="incidentDateDescription">Description of Incident Date</Label>
          <Textfield id="incidentDateDescription" value={incidentDateDescription} onChange={onChangeIncidentDateDescription} onBlur={onBlurIncidentDateDescription} maxLength={3000} />
          <Label labelFor="reportAnnotation">Report Annotation</Label>
          <Select id="reportAnnotation" isMulti value={reportAnnotation} options={reportAnnotationOptions} onChange={onChangeReportAnnotation} />
        </>
      )}
    </>
  );
};

ForgeReconciler.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
