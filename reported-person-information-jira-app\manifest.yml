modules:
  jira:issueContext:
    - key: reported-person-information-issue-context
      resource: main
      resolver:
        function: resolver
      render: native
      title: Reported Person Information
      description: Reported Person Information
      label: Reported Person Information
  function:
    - key: resolver
      handler: index.handler
resources:
  - key: main
    path: src/frontend/index.jsx
app:
  runtime:
    name: nodejs22.x
    memoryMB: 256
    architecture: arm64
  id: ari:cloud:ecosystem::app/7177cfff-9f63-4891-bf2e-7f71ca336905
permissions:
  scopes:
    - storage:app
    - read:jira-user
  external:
    fetch:
      backend:
        - 'https://t2gp-social-legal-api.dev.d2dragon.net/*'
    