modules:
  jira:issueContext:
    - key: incident-information-issue-context
      resource: main
      resolver:
        function: resolver
      render: native
      title: Incident Information
      description: A section for incident information.
      label: Incident Information
  function:
    - key: resolver
      handler: index.handler
resources:
  - key: main
    path: src/frontend/index.jsx
app:
  runtime:
    name: nodejs22.x
    memoryMB: 256
    architecture: arm64
  id: ari:cloud:ecosystem::app/a3f94adb-49c3-44f4-b835-fd07177126c4
permissions:
  scopes:
    - storage:app
    - read:jira-user
  external:
    fetch:
      backend:
        - 'https://t2gp-social-legal-api.dev.d2dragon.net/*'