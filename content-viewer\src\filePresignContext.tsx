import React, { createContext, useContext, useState, ReactNode } from 'react';

type FilePresignMap = { [filename: string]: string };

interface FilePresignContextType {
  presignedUrls: FilePresignMap;
  setPresignedUrl: (filename: string, url: string) => void;
  getPresignedUrl: (filename: string) => string | undefined;
  setAllPresignedUrls: (map: FilePresignMap) => void;
}

const FilePresignContext = createContext<FilePresignContextType | undefined>(undefined);

export const usePresignedUrls = () => {
  const context = useContext(FilePresignContext);
  if (!context) throw new Error("usePresignedUrls must be used within a FilePresignProvider");
  return context;
};

export const FilePresignProvider = ({ children }: { children: ReactNode }) => {
  const [presignedUrls, setPresignedUrls] = useState<FilePresignMap>({});

  const setPresignedUrl = (filename: string, url: string) => {
    setPresignedUrls(prev => ({ ...prev, [filename]: url }));
  };

  const setAllPresignedUrls = (map: FilePresignMap) => {
    setPresignedUrls(map);
  };

  const getPresignedUrl = (filename: string) => {
    return presignedUrls[filename];
  };

  return (
    <FilePresignContext.Provider value={{ presignedUrls, setPresignedUrl, getPresignedUrl, setAllPresignedUrls }}>
      {children}
    </FilePresignContext.Provider>
  );
};
