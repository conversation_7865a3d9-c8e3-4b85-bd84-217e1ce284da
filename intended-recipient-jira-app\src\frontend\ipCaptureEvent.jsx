import React, { useState, useEffect } from 'react';
import { Label, Textfield, DatePicker, Checkbox, Select } from '@forge/react';
import { invoke } from '@forge/bridge';

const eventNames = [
  { label: 'Login', value: 'Login' },
  { label: 'Registration', value: 'Registration' },
  { label: 'Purchase', value: 'Purchase' },
  { label: 'Upload', value: 'Upload' },
  { label: 'Other', value: 'Other' },
  { label: 'Unknown', value: 'Unknown' },
]

export const IpCaptureEventFields = ({ context }) => {
  const [ipAddress, setIpAddress] = useState('');
  const [eventName, setEventName] = useState('');
  const [dateTime, setDateTime] = useState(new Date().toISOString());
  const [possibleProxy, setPossibleProxy] = useState(false);
  const [port, setPort] = useState('');

  const onChangeIpAddress = (e) => {
    setIpAddress(e.target.value);
  };

  const onBlurIpAddress = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setIpAddress', { cloudId: context.cloudId, issueId: key, ipAddress: e.target.value });
  };

  const onChangeEventName = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    setEventName(e);
    invoke('setEventName', { cloudId: context.cloudId, issueId: key, eventName: e });
  };

  const onChangeDateTime = (e) => {
    setDateTime(e.target.value);
  };

  const onBlurDateTime = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setIpEventDateTime', { cloudId: context.cloudId, issueId: key, dateTime: e.target.value });
  };

  
  const onChangePossibleProxy = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    const value = e.target.checked;
    setPossibleProxy(value);
    invoke('setPossibleProxy', { cloudId: context.cloudId, issueId: key, possibleProxy: value });
  };

  const onChangePort = (e) => {
    setPort(e.target.value);
  };

  const onBlurPort = (e) => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;
    const { key } = context.extension.issue;
    invoke('setPort', { cloudId: context.cloudId, issueId: key, port: e.target.value });
  };

  useEffect(() => {
    if (context === undefined || context.cloudId === undefined || context.extension.issue === undefined) return;

    const { key } = context.extension.issue;

    const fetchIpCaptureData = async () => {
      try {
        const [
          ipAddress,
          eventName,
          dateTime,
          possibleProxy,
          port,
        ] = await Promise.all([
          invoke('getIpAddress', { issueId: key }),
          invoke('getEventName', { issueId: key }),
          invoke('getIpEventDateTime', { issueId: key }),
          invoke('getPossibleProxy', { issueId: key }),
          invoke('getPort', { issueId: key }),
        ]);

        setIpAddress(ipAddress);
        setEventName(JSON.parse(eventName));
        setDateTime(dateTime);
        setPossibleProxy(possibleProxy);
        setPort(port);
      } catch (error) {
        console.error('Failed to fetch IP capture data:', error);
      }
    };

    fetchIpCaptureData();
  }, [context]);

  return (
    <>
      <Label labelFor="ipAddress">IP Address</Label>
      <Textfield id="ipAddress" value={ipAddress} onChange={onChangeIpAddress} onBlur={onBlurIpAddress}/>
      <Label labelFor="eventName">Event Name</Label>
      <Select id="eventName" options={eventNames} value={eventName} onChange={onChangeEventName} />
      <Label labelFor="dateTime">IP Event Time</Label>
      <Textfield type='datetime-local' id="dateTime" value={dateTime} onChange={onChangeDateTime} onBlur={onBlurDateTime} />
      <Label labelFor="possibleProxy">Possible Proxy</Label>
      <Checkbox id="possibleProxy" isChecked={possibleProxy} onChange={onChangePossibleProxy} />
      <Label labelFor="port">Port</Label>
      <Textfield id="port" type='number' value={port} onChange={onChangePort} onBlur={onBlurPort} min={1} max={65535}/>
    </>
  );
}